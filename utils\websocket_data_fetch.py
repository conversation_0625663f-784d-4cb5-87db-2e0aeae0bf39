import pandas as pd
from datetime import datetime, time as dt_time, timedelta
import time
import sys
import config.config as config
import os
from functools import wraps
import pytz
import logging
from utils.processing_functions import process_dataframe

def retry_on_file_lock(max_attempts=3, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempts = 0
            while attempts < max_attempts:
                try:
                    return func(*args, **kwargs)
                except (PermissionError, ValueError, pd.errors.EmptyDataError) as e:
                    attempts += 1
                    if attempts == max_attempts:
                        raise e
                    print(f"File access error, retrying in {delay} seconds... (Attempt {attempts}/{max_attempts})")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

@retry_on_file_lock(max_attempts=3, delay=1)
def read_csv_safely(file_path):
    # Check if file exists and is not empty
    if not os.path.exists(file_path):
        raise ValueError(f"File does not exist: {file_path}")
    
    if os.path.getsize(file_path) == 0:
        raise ValueError(f"File is empty: {file_path}")

    # Try to read the CSV file
    try:
        df = pd.read_csv(file_path)
        if df.empty:
            raise ValueError(f"Empty dataframe from file: {file_path}")
        return df
    except pd.errors.EmptyDataError as e:
        raise ValueError(f"Empty or invalid CSV file: {file_path}") from e

def fetch_index_rows(interval, tradingsymbol):
    """
    Fetch Index data for {instrument['tradingsymbol']} Index from websocket directory.
    """
    logging.info(f"Entering function: Fetch_index_rows for interval {interval} and {tradingsymbol}")
    now = datetime.now()
    today = now.date()
    
    if now.time() < config.MARKET_START:
        end_date = today - timedelta(days=1)
        while end_date.weekday() > 4:
            end_date -= timedelta(days=1)
    else:
        end_date = today
    date_dir_name = end_date.strftime("%Y-%m-%d")

    tradingsymbol_lowercase = tradingsymbol.lower()

    #print("End date: ",end_date)

    if interval == "1":
        csv_file_name = f"{tradingsymbol_lowercase}_candles_1min.csv"
    elif interval == "5":
        csv_file_name = f"{tradingsymbol_lowercase}_candles_5min.csv"
    elif interval == "60":
        csv_file_name = f"{tradingsymbol_lowercase}_candles_1hour.csv"
    elif interval == "daily":
        csv_file_name = f"{tradingsymbol_lowercase}_candles_daily.csv"
    else:
        raise ValueError(f"Invalid interval: {interval}")
    
    #os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    day_dir = os.path.join(config.INPUT_INDEX_DIR, date_dir_name)
    instrument_dir = os.path.join(day_dir, tradingsymbol)
    input_csv_path = os.path.join(instrument_dir, csv_file_name)
    logging.info(f"Input file for index: {input_csv_path}")

    required_columns = {"timestamp", "open", "high", "low", "close", "volume"}

    # Step 1: Read the CSV
    try:
        # Check if file exists and is not empty
        # if not os.path.exists(input_csv_path):
        #     raise FileNotFoundError(f"Input file not found: {input_csv_path}")
            
        # if os.path.getsize(input_csv_path) == 0:
        #     raise ValueError(f"Input file is empty: {input_csv_path}")
        
        #df = pd.read_csv(input_csv_path)
        df = read_csv_safely(input_csv_path)
    
        if df is not None and not df.empty:   
            # Step 2: Validate required columns
            if not required_columns.issubset(df.columns):
                missing = required_columns - set(df.columns)
                raise ValueError(f"Missing required columns: {missing}")
            
            
            # Step 3: Parse timestamps (assuming they contain timezone information)
            try:
                df["timestamp"] = pd.to_datetime(df["timestamp"], format='mixed')
                # print(df.head(20))
            except Exception as e:
                raise ValueError(f"Error parsing 'timestamp' column: {e}")
                
            df = df[["timestamp", "open", "high", "low", "close", "volume"]]
            # Optional: Sort the data just in case
            df = df.sort_values("timestamp").reset_index(drop=True)
            
            from_date = df.iloc[0]["timestamp"]
            to_date = df.iloc[-1]["timestamp"]
            logging.info(f"Data fetched from {from_date} to {to_date} for {tradingsymbol} for interval {interval}")
                
            # print(df.tail(2))  # Print last 2 rows for debugging
            logging.info(f"Data fetched from {from_date} to {to_date}")
            return df, str(from_date)[:10], str(to_date)[:10]
        else:
            raise ValueError(f"No trading data found for date {tradingsymbol}")
            return None, None, None
    except pd.errors.EmptyDataError as e:
        logging.error(f"Empty or invalid CSV file: {input_csv_path}")
        raise ValueError(f"Empty or invalid CSV file: {input_csv_path}") from e
        return None, None, None
    except Exception as e:
        logging.error(f"Error reading CSV file {input_csv_path}: {str(e)}")
        raise ValueError(f"Error reading CSV file: {str(e)}") from e
        return None, None, None

def fetch_options_rows(option_type=None, instrument_name=None):
    """
    Fetch option data from Dhan API for a specific security and interval.
    """
    logging.info(f"Entering function: Fetch_options_rows for {option_type} and {instrument_name}")
    now = datetime.now()
    today = now.date()

    if now.time() < config.MARKET_START:
        end_date = today - timedelta(days=1)
        while end_date.weekday() > 4:
            end_date -= timedelta(days=1)
    else:
        end_date = today
    date_dir_name = end_date.strftime("%Y-%m-%d")

    # Check if options CSV file exists
    if not os.path.exists(config.OPTIONS_CSV_FILE):
        logging.error(f"  ✗ Options CSV file not found: {config.OPTIONS_CSV_FILE}")
        return {"success": False, "error": "CSV file not found"}, None

    # Read options CSV
    user_df = pd.read_csv(config.OPTIONS_CSV_FILE)

    if user_df.empty:
        logging.error(f"  ✗ Options CSV file is empty")
        return {"success": False, "error": "CSV file is empty"}, None
    
    user_df = user_df[
            (user_df['DISPLAY_NAME'].astype(str).str.split().str[-1] == option_type) &
            (user_df['DISPLAY_NAME'].astype(str).str.split().str[0] == config.instruments_list[0]['tradingsymbol'])
        ]
    
    # get the display_name from the filtered CSV
    if user_df.empty:
        error_msg = f"No option found for type '{option_type}' and symbol '{config.instruments_list[0]['tradingsymbol']}'"
        logging.error(f"  ✗ {error_msg}")
        return {"success": False, "error": error_msg}, None
    
    display_name = user_df['DISPLAY_NAME'].iloc[0]
    clean_symbol = display_name.replace(" ", "_").replace("/", "_")
    clean_symbol_lowercase = clean_symbol.lower()

    # Create options output directory
    os.makedirs(config.OPTIONS_OUTPUT_DIR, exist_ok=True)
    day_dir = os.path.join(config.OPTIONS_OUTPUT_DIR, date_dir_name)
    os.makedirs(day_dir, exist_ok=True)

    # Initialize master DataFrame dictionary for in-memory storage
    results = {
        "success": True,
        "total_entries": len(user_df),
        "processed_entries": 0,
        "successful_fetches": 0,
        "failed_fetches": 0,
        "details": []
    }
    master_dataframes = {}
    
    # Create options input directory
    opt_day_dir = os.path.join(config.INPUT_OPTIONS_DIR, date_dir_name)
    instrument_dir = os.path.join(opt_day_dir, clean_symbol)
    logging.info(f"Input directory for options: {instrument_dir}")    

    for interval in config.OPTION_TIMEFRAMES:
        df_opt = None
        csv_file_name = f"{clean_symbol_lowercase}_candles_{interval}min.csv"
        input_csv_path = os.path.join(instrument_dir, csv_file_name)
        logging.info(f"Input file for options: {input_csv_path} for interval {interval}")
        
        required_columns = {"timestamp", "open", "high", "low", "close", "volume"}

        # Step 1: Read the CSV
        try:
            # Check if file exists and is not empty
            # if not os.path.exists(input_csv_path):
            #     raise FileNotFoundError(f"Input file not found: {input_csv_path}")
                
            # if os.path.getsize(input_csv_path) == 0:
            #     raise ValueError(f"Input file is empty: {input_csv_path}")
            
            #df_opt = pd.read_csv(input_csv_path)
            df_opt = read_csv_safely(input_csv_path)
        
            if df_opt is not None and not df_opt.empty:        
                # Step 2: Validate required columns
                if not required_columns.issubset(df_opt.columns):
                    missing = required_columns - set(df_opt.columns)
                    raise ValueError(f"Missing required columns: {missing}")
                else: 
                    # Step 3: Parse timestamps (assuming they contain timezone information)
                    try:
                        df_opt["timestamp"] = pd.to_datetime(df_opt["timestamp"], format='mixed')
                        # print(df.head(20))
                    except Exception as e:
                        raise ValueError(f"Error parsing 'timestamp' column: {e}")
                
                    df_opt = df_opt[["timestamp", "open", "high", "low", "close", "volume"]]
                    # Step 4: Optional: Sort the data just in case
                    df_opt = df_opt.sort_values("timestamp").reset_index(drop=True)
                    
                    from_date_full = df_opt.iloc[0]["timestamp"]
                    to_date_full = df_opt.iloc[-1]["timestamp"]
                    from_date = str(from_date_full)[:10]
                    to_date = str(to_date_full)[:10]
                    logging.info(f"Data fetched from {from_date_full} to {to_date_full} for {display_name} for interval {interval}")
                
                    # Step 5: Get all data into master_dataframe
                        
                    if display_name not in master_dataframes:
                        master_dataframes[display_name] = {"Option_symbol": display_name}

                    if interval == "1":
                        now = datetime.datetime.now()
                        now_timestamp = pd.Timestamp(now)
                        current_time = now.time()
                        # Always Check if fresh data is being processed
                        if (current_time >= config.START_TRADE and current_time <= config.STOP_TRADE and
                            now_timestamp > (df_processed['timestamp'].max() + pd.Timedelta(minutes=1))):
                            logging.info(f"Fresh data not coming in for Options. Please check Websocket connection")
                            sys.exit(1)

                    df_processed, Zones_affected = process_dataframe(df_opt)
                    master_dataframes[display_name][f"df_opt_{interval}min"] = df_processed
                    

                    interval_suffix = f"{interval}min"
                    output_filename = f"{clean_symbol}_{interval_suffix}_{from_date}_to_{to_date}_processed.csv"
                    
                    output_path = os.path.join(day_dir, output_filename)
                    df_processed.to_csv(output_path, index=False)
                    logging.info(f"     Saved {interval}min data: {output_filename}")
            else:
                raise ValueError(f"No trading data found for date {display_name}")
                return None, None, None
        except pd.errors.EmptyDataError as e:
            logging.error(f"Empty or invalid CSV file: {input_csv_path}")
            raise ValueError(f"Empty or invalid CSV file: {input_csv_path}") from e
            return None, None, None
        except Exception as e:
            logging.error(f"Error reading CSV file {input_csv_path}: {str(e)}")
            raise ValueError(f"Error reading CSV file: {str(e)}") from e
            return None, None, None
                    
                            
    # Return enhanced results with master dataframes
    results["dataframes"] = master_dataframes
    
    #trading_symbol = config.instruments_list[0]['tradingsymbol']
    lot_size = config.Lot_size_list[0][instrument_name]
    print(f"lot_size of {instrument_name}: {lot_size}")
    return results, lot_size

    
