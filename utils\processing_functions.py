import pandas as pd
import pandas_ta as ta
import numpy as np
import logging

import warnings
# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')




def calculate_wma(df, price_column='close', periods=[5, 9, 13, 26, 45, 65, 90, 225, 325]):
    """
    Calculate Weighted Moving Averagess for specified periods using pandas_ta.

    Args:
        df (pd.DataFrame): Input dataframe with price data
        price_column (str): Column name to use for WMA calculation (default: 'close')
        periods (list): List of periods for WMA calculation

    Returns:
        pd.DataFrame: DataFrame with added WMA columns
    """
    df_copy = df.copy()

    # Ensure the price column exists
    if price_column not in df_copy.columns:
        #print(f"  ✗ Price column '{price_column}' not found in dataframe")
        # Add dummy columns to avoid errors later, though processing will be invalid
        for period in periods:
             df_copy[f'WMA{period}'] = np.nan
        return df_copy

    # Calculate WMA for each period
    for period in periods:
        try:
            # Use pandas_ta to calculate WMA
            wma_values = ta.wma(df_copy[price_column], length=period).round(2)
            df_copy[f'WMA{period}'] = wma_values
            # print(f"  ✓ Calculated WMA{period}") # Keep prints for clarity during execution
        except Exception as e:
            print(f"  ✗ Error calculating WMA{period}: {str(e)}")
            #print(f"  DataFrame length: {len(df_copy)}")
            df_copy[f'WMA{period}'] = np.nan

    return df_copy


def calculate_rsi_atr(df, rsi_length=14, atr_length=14, price_cols=None):
    """
    Calculate RSI and ATR indicators and add them to the DataFrame.

    Args:
        df (pd.DataFrame): Input dataframe with price data.
        rsi_length (int): Period for RSI calculation (default: 14).
        atr_length (int): Period for ATR calculation (default: 14).
        price_cols (dict or None): Optional mapping for price columns, e.g.,
            {'close': 'Close', 'high': 'High', 'low': 'Low'}
            If None, uses 'close', 'high', 'low' as column names.

    Returns:
        pd.DataFrame: DataFrame with added 'RSI_14' and 'ATR_14' columns.
    """
    df_copy = df.copy()
    # Set default column names or use provided mapping
    close_col = price_cols['close'] if price_cols and 'close' in price_cols else 'close'
    high_col = price_cols['high'] if price_cols and 'high' in price_cols else 'high'
    low_col = price_cols['low'] if price_cols and 'low' in price_cols else 'low'

    # Check if required columns exist
    if close_col in df_copy.columns:
        try:
            df_copy[f'RSI_{rsi_length}'] = ta.rsi(df_copy[close_col], length=rsi_length).round(2)
        except Exception as e:
            print(f"  ✗ Error calculating RSI({rsi_length}): {str(e)}")
            df_copy[f'RSI_{rsi_length}'] = np.nan
    else:
        df_copy[f'RSI_{rsi_length}'] = np.nan

    if all(col in df_copy.columns for col in [high_col, low_col, close_col]):
        try:
            df_copy[f'ATR_{atr_length}'] = ta.atr(
                df_copy[high_col], df_copy[low_col], df_copy[close_col], length=atr_length
            ).round(2)
        except Exception as e:
            print(f"  ✗ Error calculating ATR({atr_length}): {str(e)}")
            df_copy[f'ATR_{atr_length}'] = np.nan
    else:
        df_copy[f'ATR_{atr_length}'] = np.nan

    return df_copy


def detect_crossovers(df, fast_wma='WMA5', slow_wma='WMA13'):
    """
    Detect crossovers between two WMA series.

    Args:
        df (pd.DataFrame): DataFrame containing WMA columns
        fast_wma (str): Column name for fast WMA (default: 'WMA5')
        slow_wma (str): Column name for slow WMA (default: 'WMA13')

    Returns:
        pd.DataFrame: DataFrame with added crossover signal column
    """
    df_copy = df.copy()

    # Use standard column name for WMA5/WMA13 crossovers, custom name for others
    if fast_wma == 'WMA5' and slow_wma == 'WMA13':
        crossover_signal_name = 'crossover_signal'
    else:
        crossover_signal_name = f'crossover_signal_{fast_wma[3:]}_{slow_wma[3:]}'

    # Check if required columns exist
    if fast_wma not in df_copy.columns or slow_wma not in df_copy.columns:
        #print(f"  ✗ Required WMA columns not found for crossover detection")
        df_copy[crossover_signal_name] = ' '
        return df_copy

    # Initialize crossover signal column
    df_copy[crossover_signal_name] = ' '

    # Calculate crossover signals
    try:
        # Get the WMA series
        fast_series = df_copy[fast_wma]
        slow_series = df_copy[slow_wma]

        # Calculate the difference and its shift
        diff = fast_series - slow_series
        diff_prev = diff.shift(1)

        # Detect crossovers
        # Upward crossover: fast WMA crosses above slow WMA
        upward_cross = (diff > 0) & (diff_prev <= 0)

        # Downward crossover: fast WMA crosses below slow WMA
        downward_cross = (diff < 0) & (diff_prev >= 0)

        # Apply signals
        df_copy.loc[upward_cross, crossover_signal_name] = 'upward'
        df_copy.loc[downward_cross, crossover_signal_name] = 'downward'

        # Count crossovers
        upward_count = upward_cross.sum()
        downward_count = downward_cross.sum()

        # print(f"  ✓ Detected {upward_count} upward and {downward_count} downward crossovers") # Keep prints

    except Exception as e:
        print(f"  ✗ Error detecting crossovers: {str(e)}")

    return df_copy


def detect_supply_demand_zones(df, crossover_column='crossover_signal'):
    """
    Detect supply and demand zones based on crossover transitions.

    Supply Zones: Price ranges between 'upward' → 'downward' crossover transitions
    - Captures the candle with highest 'high' price in the period

    Demand Zones: Price ranges between 'downward' → 'upward' crossover transitions
    - Captures the candle with lowest 'low' price in the period

    Args:
        df (pd.DataFrame): DataFrame containing crossover signals and price data
        crossover_column (str): Column name containing crossover signals

    Returns:
        pd.DataFrame: DataFrame with added supply_zone and demand_zone columns
    """
    df_copy = df.copy()

    if crossover_column == 'crossover_signal':
        col_name = 'Zones_sup_dem'
    else:
        col_name = f'Zones_sup_dem_{crossover_column[17:]}'

    # Check if required columns exist
    required_columns = [crossover_column, 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        #print(f"  ✗ Required columns not found for zone detection: {missing_columns}")
        df_copy[col_name] = ''
        return df_copy

    # Initialize zone columns
    df_copy[col_name] = ''

    try:
        # Get crossover signals
        signals = df_copy[crossover_column]

        # Find crossover indices
        upward_indices = df_copy[signals == 'upward'].index.tolist()
        downward_indices = df_copy[signals == 'downward'].index.tolist()

        supply_zones_count = 0
        demand_zones_count = 0

        # Detect Supply Zones (upward → downward transitions)
        # Iterate through upward crossovers
        for upward_idx in upward_indices:
            # Find the next downward crossover after this upward crossover
            next_downward_indices = [idx for idx in downward_indices if idx > upward_idx]

            if next_downward_indices:
                # Get the first downward crossover after the current upward one
                downward_idx = next_downward_indices[0]

                # Ensure the range is valid (upward_idx <= downward_idx)
                if upward_idx <= downward_idx:
                    # Find the candle with highest 'high' between upward and the next downward crossover
                    zone_data = df_copy.loc[upward_idx:downward_idx]
                    if not zone_data.empty:
                        max_high_idx = zone_data['high'].idxmax()
                        df_copy.loc[max_high_idx, col_name] = 'Supply'
                        supply_zones_count += 1

        # Detect Demand Zones (downward → upward transitions)
        # Iterate through downward crossovers
        for downward_idx in downward_indices:
            # Find the next upward crossover after this downward crossover
            next_upward_indices = [idx for idx in upward_indices if idx > downward_idx]

            if next_upward_indices:
                # Get the first upward crossover after the current downward one
                upward_idx = next_upward_indices[0]

                # Ensure the range is valid (downward_idx <= upward_idx)
                if downward_idx <= upward_idx:
                    # Find the candle with lowest 'low' between downward and the next upward crossover
                    zone_data = df_copy.loc[downward_idx:upward_idx]
                    if not zone_data.empty:
                        min_low_idx = zone_data['low'].idxmin()
                        df_copy.loc[min_low_idx, col_name] = 'Demand'
                        demand_zones_count += 1

        # print(f"  ✓ Detected {supply_zones_count} supply zones and {demand_zones_count} demand zones") # Keep prints

    except Exception as e:
        print(f"  ✗ Error detecting supply/demand zones: {str(e)}")

    return df_copy


def validate_zones(df, Zones_column='Zones_sup_dem'):
    """
    Validate supply and demand zones by adding a Zone_Status column.

    Takes a DataFrame with already identified Supply and Demand zones
    (where 'Zones_sup_dem' column contains 'Supply' or 'Demand' values)
    and adds a new column 'Zone_Status' with default value 'Valid' for each identified zone.

    Args:
        df (pd.DataFrame): DataFrame containing identified supply/demand zones

    Returns:
        pd.DataFrame: DataFrame with added Zone_Status column
    """
    df_copy = df.copy()
    if Zones_column == 'Zones_sup_dem':
        col_name = 'Zone_Status'
    else:
        col_name = f'Zone_Status_{Zones_column[14:]}'

    # Check if required column exists
    if Zones_column not in df_copy.columns:
        #print("  ✗ Required column 'Zones_sup_dem' not found for zone validation")
        df_copy[col_name] = ''
        return df_copy

    try:
        # Initialize Zone_Status column with empty values
        df_copy[col_name] = ''

        # Set 'Valid' status for identified zones
        zone_mask = (df_copy[Zones_column] == 'Supply') | (df_copy[Zones_column] == 'Demand')
        df_copy.loc[zone_mask, col_name] = 'Valid'

        # Count validated zones
        supply_zones = (df_copy[Zones_column] == 'Supply').sum()
        demand_zones = (df_copy[Zones_column] == 'Demand').sum()
        total_zones = supply_zones + demand_zones

        #print(f"  ✓ Validated {total_zones} zones ({supply_zones} supply, {demand_zones} demand)")

    except Exception as e:
        print(f"  ✗ Error validating zones: {str(e)}")
        df_copy['Zone_Status'] = ''

    return df_copy

def update_supply_zone_status(df, crossover_column='crossover_signal', Zones_column='Zones_sup_dem', Zone_Status_column='Zone_Status'):
    """
    Two-stage update of Supply zone status based on price action validation.

    Stage 1: Process historical data (excluding the most recent candle)
        - Executes zone validation logic on all but the last candle.
        - Updates zone statuses for historical data.

    Stage 2: Process with latest candle
        - Adds the most recent candle to the Stage 1 output.
        - Executes zone validation logic again.
        - Tracks zones updated specifically in Stage 2.

    Args:
        df (pd.DataFrame): DataFrame with validated zones containing Zone_Status_column column

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]:
            - Fully updated DataFrame (Stage 2)
            - DataFrame of zones modified in Stage 2
    """
    df_copy = df.copy()
    if df_copy.empty:
        return df_copy, pd.DataFrame()

    # --- Stage 1: Process all except the most recent candle ---
    historical_df = df_copy.iloc[:-1].copy()
    recent_candle = df_copy.iloc[[-1]].copy()  # DataFrame with just the last row

    # Run existing logic on historical_df
    try:
        required_columns = [Zones_column, Zone_Status_column, 'high', 'low', crossover_column]
        missing_columns = [col for col in required_columns if col not in historical_df.columns]
        if missing_columns:
            return df_copy, pd.DataFrame()

        # Get supply zones with 'Valid' or 'Tested' status
        supply_zones = historical_df[
            ((historical_df[Zones_column] == 'Supply') & (historical_df[Zone_Status_column] == 'Valid')) |
            ((historical_df[Zones_column] == 'Supply') & (historical_df[Zone_Status_column] == 'Tested'))
        ]
        invalid_count = 0
        tested_count = 0

        for zone_idx in supply_zones.index:
            zone_high = historical_df.loc[zone_idx, 'high']
            zone_low = historical_df.loc[zone_idx, 'low']
            zone_crossover = historical_df.loc[zone_idx, crossover_column]

            subsequent_data = historical_df.loc[zone_idx + 1:]
            if subsequent_data.empty:
                continue

            # Invalidation: any subsequent high > zone high
            invalidation_check = subsequent_data['high'] > zone_high
            if invalidation_check.any():
                historical_df.loc[zone_idx, Zone_Status_column] = 'Invalid'
                invalid_count += 1
                continue

            # Test check logic
            test_triggered = False
            if 'downward' in zone_crossover:
                test_check = subsequent_data['high'] >= zone_low
                if test_check.any():
                    test_triggered = True
            else:
                downward_signals = subsequent_data[subsequent_data[crossover_column] == 'downward']
                if not downward_signals.empty:
                    next_downward_idx = downward_signals.index[0]
                    after_downward = historical_df.loc[next_downward_idx + 1:]
                    if not after_downward.empty:
                        test_check = after_downward['high'] >= zone_low
                        if test_check.any():
                            test_triggered = True

            if test_triggered:
                historical_df.loc[zone_idx, Zone_Status_column] = 'Tested'
                tested_count += 1

    except Exception as e:
        print(f"  ✗ Error updating supply zone status (Stage 1): {str(e)}")
        return df_copy, pd.DataFrame()

    # --- Stage 2: Process with latest candle ---
    # Combine historical_df and recent_candle
    stage2_df = pd.concat([historical_df, recent_candle]).copy()
    affected_zones_stage2 = []

    try:
        required_columns = [Zones_column, Zone_Status_column, 'high', 'low', crossover_column]
        missing_columns = [col for col in required_columns if col not in stage2_df.columns]
        if missing_columns:
            return stage2_df, pd.DataFrame()

        supply_zones = stage2_df[
            ((stage2_df[Zones_column] == 'Supply') & (stage2_df[Zone_Status_column] == 'Valid')) |
            ((stage2_df[Zones_column] == 'Supply') & (stage2_df[Zone_Status_column] == 'Tested'))
        ]
        recent_candle_idx = stage2_df.index[-1]

        for zone_idx in supply_zones.index:
            zone_high = stage2_df.loc[zone_idx, 'high']
            zone_low = stage2_df.loc[zone_idx, 'low']
            zone_crossover = stage2_df.loc[zone_idx, crossover_column]

            subsequent_data = stage2_df.loc[zone_idx + 1:]
            if subsequent_data.empty:
                continue

            # Invalidation: any subsequent high > zone high
            invalidation_check = subsequent_data['high'] > zone_high
            if invalidation_check.any():
                stage2_df.loc[zone_idx, Zone_Status_column] = 'Invalid'
                # Only track if recent candle caused invalidation
                if recent_candle_idx in invalidation_check[invalidation_check].index:
                    affected_zones_stage2.append(stage2_df.loc[[zone_idx]])
                continue

            # Test check logic
            test_triggered = False
            if 'downward' in zone_crossover:
                test_check = subsequent_data['high'] >= zone_low
                if test_check.any():
                    test_triggered = True
                    Zone_status_prior_to_changing = stage2_df.loc[zone_idx, Zone_Status_column]
                    if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
                        stage2_df.loc[zone_idx, Zone_Status_column] = 'Tested'
                        affected_zones_stage2.append(stage2_df.loc[[zone_idx]])
            else:
                downward_signals = subsequent_data[subsequent_data[crossover_column] == 'downward']
                if not downward_signals.empty:
                    next_downward_idx = downward_signals.index[0]
                    after_downward = stage2_df.loc[next_downward_idx + 1:]
                    if not after_downward.empty:
                        test_check = after_downward['high'] >= zone_low
                        if test_check.any():
                            test_triggered = True
                            Zone_status_prior_to_changing = stage2_df.loc[zone_idx, Zone_Status_column]
                            if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
                                stage2_df.loc[zone_idx, Zone_Status_column] = 'Tested'
                                affected_zones_stage2.append(stage2_df.loc[[zone_idx]])

            if test_triggered and not (
                'downward' in zone_crossover and
                recent_candle_idx in test_check[test_check].index and
                Zone_status_prior_to_changing == 'Valid'
            ):
                stage2_df.loc[zone_idx, Zone_Status_column] = 'Tested'

    except Exception as e:
        print(f"  ✗ Error updating supply zone status (Stage 2): {str(e)}")

    # Concatenate affected zones for Stage 2
    result_df_stage2 = pd.concat(affected_zones_stage2) if affected_zones_stage2 else pd.DataFrame()
    return stage2_df, result_df_stage2

def update_demand_zone_status(df, crossover_column='crossover_signal', Zones_column='Zones_sup_dem', Zone_Status_column='Zone_Status'):
    """
    Two-stage update of Demand zone status based on price action validation.

    Stage 1: Process historical data (excluding the most recent candle)
        - Executes zone validation logic on all but the last candle.
        - Updates zone statuses for historical data.

    Stage 2: Process with latest candle
        - Adds the most recent candle to the Stage 1 output.
        - Executes zone validation logic again.
        - Tracks zones updated specifically in Stage 2.

    Args:
        df (pd.DataFrame): DataFrame with validated zones containing Zone_Status_column column

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: 
            - Fully updated DataFrame (Stage 2)
            - DataFrame of zones modified in Stage 2
    """
    df_copy = df.copy()
    if df_copy.empty:
        return df_copy, pd.DataFrame()

    # --- Stage 1: Process all except the most recent candle ---
    historical_df = df_copy.iloc[:-1].copy()
    recent_candle = df_copy.iloc[[-1]].copy()  # DataFrame with just the last row

    # Run existing logic on historical_df
    try:
        required_columns = [Zones_column, Zone_Status_column, 'high', 'low', crossover_column]
        missing_columns = [col for col in required_columns if col not in historical_df.columns]
        if missing_columns:
            return df_copy, pd.DataFrame()

        # Get demand zones with 'Valid' or 'Tested' status
        demand_zones = historical_df[
            ((historical_df[Zones_column] == 'Demand') & (historical_df[Zone_Status_column] == 'Valid')) |
            ((historical_df[Zones_column] == 'Demand') & (historical_df[Zone_Status_column] == 'Tested'))
        ]
        invalid_count = 0
        tested_count = 0

        for zone_idx in demand_zones.index:
            zone_high = historical_df.loc[zone_idx, 'high']
            zone_low = historical_df.loc[zone_idx, 'low']
            zone_crossover = historical_df.loc[zone_idx, crossover_column]

            subsequent_data = historical_df.loc[zone_idx + 1:]
            if subsequent_data.empty:
                continue

            # Invalidation: any subsequent low < zone low
            invalidation_check = subsequent_data['low'] < zone_low
            if invalidation_check.any():
                historical_df.loc[zone_idx, Zone_Status_column] = 'Invalid'
                invalid_count += 1
                continue

            # Test check logic
            test_triggered = False
            if 'upward' in zone_crossover:
                test_check = subsequent_data['low'] <= zone_high
                if test_check.any():
                    test_triggered = True
            else:
                upward_signals = subsequent_data[subsequent_data[crossover_column] == 'upward']
                if not upward_signals.empty:
                    next_upward_idx = upward_signals.index[0]
                    after_upward = historical_df.loc[next_upward_idx + 1:]
                    if not after_upward.empty:
                        test_check = after_upward['low'] <= zone_high
                        if test_check.any():
                            test_triggered = True

            if test_triggered:
                historical_df.loc[zone_idx, Zone_Status_column] = 'Tested'
                tested_count += 1

    except Exception as e:
        print(f"  ✗ Error updating demand zone status (Stage 1): {str(e)}")
        return df_copy, pd.DataFrame()

    # --- Stage 2: Process with latest candle ---
    # Combine historical_df and recent_candle
    stage2_df = pd.concat([historical_df, recent_candle]).copy()
    affected_zones_stage2 = []

    try:
        required_columns = [Zones_column, Zone_Status_column, 'high', 'low', crossover_column]
        missing_columns = [col for col in required_columns if col not in stage2_df.columns]
        if missing_columns:
            return stage2_df, pd.DataFrame()

        demand_zones = stage2_df[
            ((stage2_df[Zones_column] == 'Demand') & (stage2_df[Zone_Status_column] == 'Valid')) |
            ((stage2_df[Zones_column] == 'Demand') & (stage2_df[Zone_Status_column] == 'Tested'))
        ]
        recent_candle_idx = stage2_df.index[-1]

        for zone_idx in demand_zones.index:
            zone_high = stage2_df.loc[zone_idx, 'high']
            zone_low = stage2_df.loc[zone_idx, 'low']
            zone_crossover = stage2_df.loc[zone_idx, crossover_column]

            subsequent_data = stage2_df.loc[zone_idx + 1:]
            if subsequent_data.empty:
                continue

            # Invalidation: any subsequent low < zone low
            invalidation_check = subsequent_data['low'] < zone_low
            if invalidation_check.any():
                stage2_df.loc[zone_idx, Zone_Status_column] = 'Invalid'
                # Only track if recent candle caused invalidation
                if recent_candle_idx in invalidation_check[invalidation_check].index:
                    affected_zones_stage2.append(stage2_df.loc[[zone_idx]])
                continue

            # Test check logic
            test_triggered = False
            if 'upward' in zone_crossover:
                test_check = subsequent_data['low'] <= zone_high
                if test_check.any():
                    test_triggered = True
                    Zone_status_prior_to_changing = stage2_df.loc[zone_idx, Zone_Status_column]
                    if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
                        stage2_df.loc[zone_idx, Zone_Status_column] = 'Tested'
                        affected_zones_stage2.append(stage2_df.loc[[zone_idx]])
            else:
                upward_signals = subsequent_data[subsequent_data[crossover_column] == 'upward']
                if not upward_signals.empty:
                    next_upward_idx = upward_signals.index[0]
                    after_upward = stage2_df.loc[next_upward_idx + 1:]
                    if not after_upward.empty:
                        test_check = after_upward['low'] <= zone_high
                        if test_check.any():
                            test_triggered = True
                            Zone_status_prior_to_changing = stage2_df.loc[zone_idx, Zone_Status_column]
                            if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
                                stage2_df.loc[zone_idx, Zone_Status_column] = 'Tested'
                                affected_zones_stage2.append(stage2_df.loc[[zone_idx]])

            if test_triggered and not (
                'upward' in zone_crossover and
                recent_candle_idx in test_check[test_check].index and
                Zone_status_prior_to_changing == 'Valid'
            ):
                stage2_df.loc[zone_idx, Zone_Status_column] = 'Tested'

    except Exception as e:
        print(f"  ✗ Error updating demand zone status (Stage 2): {str(e)}")

    # Concatenate affected zones for Stage 2
    result_df_stage2 = pd.concat(affected_zones_stage2) if affected_zones_stage2 else pd.DataFrame()
    return stage2_df, result_df_stage2


def detect_crossovers_diff_timeframes(df_first, df_second, wma_col_df1, wma_col_df2):
    """
    Detect crossovers between a WMA from a short timeframe dataframe (df_first) and a WMA from a longer timeframe dataframe (df_second).

    Args:
        df_first (pd.DataFrame): Primary dataframe (shorter timeframe, e.g., 1-minute data)
        df_second (pd.DataFrame): Secondary dataframe (longer timeframe, e.g., 5-minute data)
        wma_col_df1 (str): Column name of the WMA indicator in df_first (e.g., 'WMA9')
        wma_col_df2 (str): Column name of the WMA indicator in df_second (e.g., 'WMA5')

    Returns:
        pd.DataFrame: df_first with an added crossover signal column
    """
    df1 = df_first.copy()
    df2 = df_second.copy()

    # Ensure both dataframes have a timestamp column and are sorted
    if 'timestamp' not in df1.columns or 'timestamp' not in df2.columns:
        #print("  ✗ Both dataframes must have a 'timestamp' column.")
        df1[f'crossover_signal_{wma_col_df1[3:]}_{wma_col_df2[3:]}_htf2'] = ' '
        return df1

    df1['timestamp'] = pd.to_datetime(df1['timestamp'])
    df2['timestamp'] = pd.to_datetime(df2['timestamp'])
    df1 = df1.sort_values('timestamp').reset_index(drop=True)
    df2 = df2.sort_values('timestamp').reset_index(drop=True)

    # Prepare df2 for merging: keep only timestamp and WMA column
    df2_merge = df2[['timestamp', wma_col_df2]].copy()
    # Forward-fill df2's WMA to all df1 timestamps using merge_asof
    df1 = pd.merge_asof(
        df1,
        df2_merge.rename(columns={wma_col_df2: f'{wma_col_df2}_tf'}),
        on='timestamp',
        direction='backward'
    )

    # Check if required columns exist
    if wma_col_df1 not in df1.columns or f'{wma_col_df2}_tf' not in df1.columns:
        df1[f'crossover_signal_{wma_col_df1[3:]}_{wma_col_df2[3:]}_htf2'] = ' '
        return df1

    # Calculate crossover signals
    fast_series = df1[wma_col_df1]
    slow_series = df1[f'{wma_col_df2}_tf']
    diff = fast_series - slow_series
    diff_prev = diff.shift(1)
    
    upward_cross = (diff > 0) & (diff_prev <= 0)
    downward_cross = (diff < 0) & (diff_prev >= 0)

    crossover_signal_name = f'crossover_signal_{wma_col_df1[3:]}_{wma_col_df2[3:]}_htf2'
    df1[crossover_signal_name] = ' '
    df1.loc[upward_cross, crossover_signal_name] = 'upward'
    df1.loc[downward_cross, crossover_signal_name] = 'downward'

    # Delete the temporary column before returning 
    del df1[f'{wma_col_df2}_tf'] 
    return df1

def identify_zone_on_zone(df, candle_info, zone_type):
    """
    Detects when a candle intersects with existing supply or demand zones.

    Args:
        df (pd.DataFrame): DataFrame containing zone data (must have 'Zones_sup_dem', 'Zone_Status', 'high', 'low')
        candle_info (dict): Dictionary or object containing candle data with 'high' and 'low' price values
        zone_type (str): Type of zone to check for intersection ("Demand" or "Supply")

    Returns:
        pd.DataFrame: DataFrame containing all matching zone records where the candle intersects the zone.
                      Returns empty DataFrame if no matches found or on error.
    """
    # Validate inputs
    if df is None or df.empty or not isinstance(df, pd.DataFrame):
        return pd.DataFrame()
    # if not isinstance(candle_info, dict) or 'high' not in candle_info or 'low' not in candle_info:
    #     return pd.DataFrame()
    if zone_type not in ['Demand', 'Supply']:
        return pd.DataFrame()

    matching_zones = pd.DataFrame()
    
    # Required columns
    required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low']
    if any(col not in df.columns for col in required_columns):
        return pd.DataFrame()

    try:
        # Filter zones by type and status
        zone_mask = (
            (df['Zones_sup_dem'] == zone_type) &
            (df['Zone_Status'].isin(['Valid', 'Tested']))
        )
        zones_df = df[zone_mask]

        if zones_df.empty:
            return pd.DataFrame()

        # Intersection logic
        if zone_type == "Demand":
            # Candle's low intersects with Demand zone
            intersect_mask = (
                (candle_info['low'] <= zones_df['high']) &
                (candle_info['low'] > zones_df['low'])
            )
        else:  # Supply
            # Candle's high intersects with Supply zone
            intersect_mask = (
                (candle_info['high'] >= zones_df['low']) &
                (candle_info['high'] < zones_df['high'])
            )

        matching_zones = zones_df[intersect_mask]

        if matching_zones.empty:
            return pd.DataFrame()
        else:
            return matching_zones

    except Exception as e:
        print(f"  ✗ Error in identify_zone_on_zone: {str(e)}")
        return pd.DataFrame()

def check_zone_over_zone(df, option_type, latest_zone, prior_to_latest_zone, two_prior_to_latest_zone):
    logging.info(f"Entering function: check_zone_over_zone for {option_type}")
    if df is None or df.empty:
        return False
    if latest_zone is None or prior_to_latest_zone is None or two_prior_to_latest_zone is None:
        return False
    
    if option_type == "CALL":
        if two_prior_to_latest_zone['Zones_sup_dem'] == 'Demand':
            zone_to_check = two_prior_to_latest_zone
        else:
            zone_to_check = prior_to_latest_zone

        logging.info(f"zone_to_check: {zone_to_check['timestamp']}")
        matching_row = df[df['timestamp'] == zone_to_check['timestamp']]
        matching_index = matching_row.index[0]
        row_position = df.index.get_loc(matching_index)
        if row_position + 2 < len(df):
            next_entry = df.iloc[row_position + 1]
            next_to_next_entry = df.iloc[row_position + 2]
        else:
            next_entry = None
            next_to_next_entry = None

        if (latest_zone['low'] <= next_entry['high'] and 
            latest_zone['low'] > next_entry['low']):
            logging.info(f"Zone over zone detected CALLS (first candle after zone): {next_entry['timestamp']} high: {next_entry['high']} low: {next_entry['low']}")  
            return True
        elif (latest_zone['low'] <= next_to_next_entry['high'] and
            latest_zone['low'] > next_to_next_entry['low']):
            logging.info(f"Zone over zone detected CALLS (second candle after zone): {next_to_next_entry['timestamp']} high: {next_to_next_entry['high']} low: {next_to_next_entry['low']}")  
            return True
        else:
            logging.info(f"No zone over zone detected - CALLS")
            return False
        
    elif option_type == "PUT":
        if two_prior_to_latest_zone['Zones_sup_dem'] == 'Supply':
            zone_to_check = two_prior_to_latest_zone
        else:
            zone_to_check = prior_to_latest_zone

        logging.info(f"zone_to_check: {zone_to_check['timestamp']}")
        matching_row = df[df['timestamp'] == zone_to_check['timestamp']]
        matching_index = matching_row.index[0]
        row_position = df.index.get_loc(matching_index)
        if row_position + 2 < len(df):
            next_entry = df.iloc[row_position + 1]
            next_to_next_entry = df.iloc[row_position + 2]
        else:
            next_entry = None
            next_to_next_entry = None

        if (latest_zone['high'] >= next_entry['low'] and 
            latest_zone['high'] < next_entry['high']):
            logging.info(f"Zone over zone detected PUTS (first candle after zone): {next_entry['timestamp']} high: {next_entry['high']} low: {next_entry['low']}")  
            return True
        elif (latest_zone['high'] >= next_to_next_entry['low'] and
            latest_zone['high'] < next_to_next_entry['high']):
            logging.info(f"Zone over zone detected PUTS (second candle after zone): {next_to_next_entry['timestamp']} high: {next_to_next_entry['high']} low: {next_to_next_entry['low']}")  
            return True
        else:
            logging.info(f"No zone over zone detected - PUTS")
            return False
    return False

def get_resistant_zones(df, price_value, zone_type='Both'):
    """
    Identifies valid supply or demand zones based on a price value.
    
    Args:
        df (pd.DataFrame): DataFrame that has already been processed with supply/demand zones
        price_value (float): Numeric value representing the current price to check against zones
        zone_type (str): Type of zones to find ('Supply', 'Demand', or 'Both')
        
    Returns:
        pd.DataFrame: Filtered DataFrame containing only the rows that meet the conditions
    """
    df_copy = df.copy()
    
    # Check if required columns exist
    required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]
    
    if missing_columns or df_copy.empty:
        #print(f"  ✗ Required columns not found for resistant zones: {missing_columns}")
        return pd.DataFrame()
    
    try:
        # Initialize empty DataFrame for results
        result_df = pd.DataFrame()
        
        # Filter for Demand zones
        if zone_type in ['Demand', 'Both']:
            demand_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Demand') & 
                                  (df_copy['Zone_Status'].isin(['Valid', 'Tested'])) &
                                  #(df_copy['Zone_Status'].isin(['Valid'])) &
                                  (price_value <= df_copy['high']) & 
                                  (price_value > df_copy['low'])]
            result_df = pd.concat([result_df, demand_zones])
        
        # Filter for Supply zones
        if zone_type in ['Supply', 'Both']:
            supply_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Supply') & 
                                  (df_copy['Zone_Status'].isin(['Valid', 'Tested'])) &
                                  #(df_copy['Zone_Status'].isin(['Valid'])) &
                                  (price_value >= df_copy['low']) & 
                                  (price_value < df_copy['high'])]
            result_df = pd.concat([result_df, supply_zones])
        
        return result_df
    
    except Exception as e:
        print(f"  ✗ Error identifying resistant zones: {str(e)}")
        return pd.DataFrame()


def calculate_wma9_color_code(df):
    """
    Calculate WMA9 color code based on crossover signals.

    Args:
        df (pd.DataFrame): DataFrame with crossover signal columns

    Returns:
        pd.DataFrame: DataFrame with added WMA9_color_code column
    """
    df_copy = df.copy()

    # Check if DataFrame is empty
    if df_copy.empty:
        return df_copy

    # Required crossover signal columns
    required_columns = [
        'crossover_signal_5_9',
        'crossover_signal_9_20',
        'crossover_signal_9_26',
        'crossover_signal_9_45',
        'crossover_signal_9_65'
    ]

    # Check if required columns exist
    missing_columns = [col for col in required_columns if col not in df_copy.columns]
    if missing_columns:
        # Initialize with empty values if columns are missing
        df_copy['WMA9_color_code'] = ''
        return df_copy

    try:
        # Initialize the WMA9_color_code column
        df_copy['WMA9_color_code'] = ''

        # Initialize tracking variables
        WMA9_current_color = None
        crossover_processed = None
        setting_initial_color_flag = True

        # Step 1: Find the first crossover_signal_5_9 to set initial color
        for i in range(len(df_copy)):
            crossover_5_9 = df_copy.iloc[i]['crossover_signal_5_9']

            # Skip empty/NaN values
            if pd.isna(crossover_5_9) or crossover_5_9 == ' ' or crossover_5_9 == '':
                continue

            # Set initial color based on first crossover
            if crossover_5_9 == 'downward':
                WMA9_current_color = 'yellow'
                crossover_processed = 'crossover_5_9'
                df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'yellow'
                break
            elif crossover_5_9 == 'upward':
                WMA9_current_color = 'blue'
                crossover_processed = 'crossover_5_9'
                df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'blue'
                break

        # If no initial crossover found, return with empty color codes
        if WMA9_current_color is None:
            return df_copy

        # Step 2: Process subsequent rows
        start_index = None
        for i in range(len(df_copy)):
            if df_copy.iloc[i]['WMA9_color_code'] != '':
                start_index = i + 1
                break

        if start_index is None or start_index >= len(df_copy):
            return df_copy

        for i in range(start_index, len(df_copy)):
            row = df_copy.iloc[i]

            # Get crossover signals for current row
            crossover_5_9 = row['crossover_signal_5_9']
            crossover_9_20 = row['crossover_signal_9_20']
            crossover_9_26 = row['crossover_signal_9_26']
            crossover_9_45 = row['crossover_signal_9_45']
            crossover_9_65 = row['crossover_signal_9_65']

            # Clean up crossover values (treat NaN, empty string, and space as no signal)
            def clean_signal(signal):
                if pd.isna(signal) or signal == ' ' or signal == '':
                    return None
                return signal

            crossover_5_9 = clean_signal(crossover_5_9)
            crossover_9_20 = clean_signal(crossover_9_20)
            crossover_9_26 = clean_signal(crossover_9_26)
            crossover_9_45 = clean_signal(crossover_9_45)
            crossover_9_65 = clean_signal(crossover_9_65)

            # Check for 9_20+ crossovers (any of 9_20, 9_26, 9_45, 9_65)
            crossover_9_20_plus_signals = [crossover_9_20, crossover_9_26, crossover_9_45, crossover_9_65]

            # Apply color logic based on current color state
            if WMA9_current_color == 'yellow':
                # Check for downward crossovers in 9_20+ signals
                if any(signal == 'downward' for signal in crossover_9_20_plus_signals):
                    WMA9_current_color = 'orange'
                    crossover_processed = 'crossover_9_20+'
                    setting_initial_color_flag = False
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'orange'
                elif setting_initial_color_flag and crossover_5_9 == 'upward':
                    WMA9_current_color = 'blue'
                    crossover_processed = 'crossover_5_9'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'blue'
                elif not setting_initial_color_flag and crossover_5_9 == 'upward' and crossover_processed == 'crossover_5_9':
                    WMA9_current_color = 'cyan'
                    crossover_processed = 'crossover_5_9'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'cyan'
                else:
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'yellow'

            elif WMA9_current_color == 'blue':
                # Check for upward crossovers in 9_20+ signals
                if any(signal == 'upward' for signal in crossover_9_20_plus_signals):
                    WMA9_current_color = 'cyan'
                    crossover_processed = 'crossover_9_20+'
                    setting_initial_color_flag = False
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'cyan'
                elif setting_initial_color_flag and crossover_5_9 == 'downward':
                    WMA9_current_color = 'yellow'
                    crossover_processed = 'crossover_5_9'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'yellow'
                elif not setting_initial_color_flag and crossover_5_9 == 'downward' and crossover_processed == 'crossover_5_9':
                    WMA9_current_color = 'orange'
                    crossover_processed = 'crossover_5_9'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'orange'
                else:
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'blue'

            elif WMA9_current_color == 'cyan':
                if crossover_5_9 == 'downward':
                    WMA9_current_color = 'yellow'
                    crossover_processed = 'crossover_5_9'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'yellow'
                else:
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'cyan'

            elif WMA9_current_color == 'orange':
                if crossover_5_9 == 'upward':
                    WMA9_current_color = 'blue'
                    crossover_processed = 'crossover_5_9'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'blue'
                else:
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA9_color_code')] = 'orange'

    except Exception as e:
        print(f"  ✗ Error calculating WMA9 color code: {str(e)}")
        df_copy['WMA9_color_code'] = ''

    return df_copy


def calculate_wma45_wma65_color_code(df):
    """
    Calculate WMA45 and WMA65 color codes based on crossover signals and WMA values.
    Both color code columns will have identical values for each row.

    Args:
        df (pd.DataFrame): DataFrame with crossover signal columns and WMA columns

    Returns:
        pd.DataFrame: DataFrame with added WMA45_color_code and WMA65_color_code columns
    """
    df_copy = df.copy()

    # Check if DataFrame is empty
    if df_copy.empty:
        return df_copy

    # Required columns
    required_columns = [
        'crossover_signal_45_65',
        'crossover_signal_20_65',
        'WMA20',
        'WMA45',
        'WMA65'
    ]

    # Check if required columns exist
    missing_columns = [col for col in required_columns if col not in df_copy.columns]
    if missing_columns:
        # Initialize with empty values if columns are missing
        df_copy['WMA45_color_code'] = ''
        df_copy['WMA65_color_code'] = ''
        return df_copy

    try:
        # Initialize the color code columns
        df_copy['WMA45_color_code'] = ''
        df_copy['WMA65_color_code'] = ''

        # Initialize tracking variable
        WMA45_current_color = None

        # Step 1: Find the first crossover_signal_45_65 to set initial color
        for i in range(len(df_copy)):
            crossover_45_65 = df_copy.iloc[i]['crossover_signal_45_65']

            # Skip empty/NaN values
            if pd.isna(crossover_45_65) or crossover_45_65 == ' ' or crossover_45_65 == '':
                continue

            # Set initial color based on first crossover
            if crossover_45_65 == 'downward':
                WMA45_current_color = 'red'
                df_copy.iloc[i, df_copy.columns.get_loc('WMA45_color_code')] = 'red'
                df_copy.iloc[i, df_copy.columns.get_loc('WMA65_color_code')] = 'red'
                break
            elif crossover_45_65 == 'upward':
                WMA45_current_color = 'green'
                df_copy.iloc[i, df_copy.columns.get_loc('WMA45_color_code')] = 'green'
                df_copy.iloc[i, df_copy.columns.get_loc('WMA65_color_code')] = 'green'
                break

        # If no initial crossover found, return with empty color codes
        if WMA45_current_color is None:
            return df_copy

        # Step 2: Process subsequent rows
        start_index = None
        for i in range(len(df_copy)):
            if df_copy.iloc[i]['WMA45_color_code'] != '':
                start_index = i + 1
                break

        if start_index is None or start_index >= len(df_copy):
            return df_copy

        for i in range(start_index, len(df_copy)):
            row = df_copy.iloc[i]

            # Get crossover signals and WMA values for current row
            crossover_45_65 = row['crossover_signal_45_65']
            crossover_20_65 = row['crossover_signal_20_65']
            wma20 = row['WMA20']
            wma45 = row['WMA45']
            wma65 = row['WMA65']

            # Clean up crossover values (treat NaN, empty string, and space as no signal)
            def clean_signal(signal):
                if pd.isna(signal) or signal == ' ' or signal == '':
                    return None
                return signal

            crossover_45_65 = clean_signal(crossover_45_65)
            crossover_20_65 = clean_signal(crossover_20_65)

            # Apply color logic based on current color state
            if WMA45_current_color == 'red':
                if crossover_20_65 == 'upward':
                    WMA45_current_color = 'cyan'
                    color = 'cyan'
                elif crossover_45_65 == 'upward':
                    WMA45_current_color = 'green'
                    color = 'green'
                else:
                    color = 'red'  # maintain current

            elif WMA45_current_color == 'green':
                if crossover_20_65 == 'downward':
                    WMA45_current_color = 'orange'
                    color = 'orange'
                elif crossover_45_65 == 'downward':
                    # Check WMA20 vs WMA65 comparison
                    if not pd.isna(wma20) and not pd.isna(wma65):
                        if wma20 > wma65:
                            WMA45_current_color = 'cyan'
                            color = 'cyan'
                        else:
                            WMA45_current_color = 'red'
                            color = 'red'
                    else:
                        # If WMA values are NaN, default to red
                        WMA45_current_color = 'red'
                        color = 'red'
                else:
                    color = 'green'  # maintain current

            elif WMA45_current_color == 'cyan':
                if crossover_20_65 == 'downward':
                    WMA45_current_color = 'red'
                    color = 'red'
                elif crossover_45_65 == 'upward':
                    # Check WMA20 vs WMA45 comparison
                    if not pd.isna(wma20) and not pd.isna(wma45):
                        if wma20 >= wma45:
                            WMA45_current_color = 'green'
                            color = 'green'
                        else:
                            WMA45_current_color = 'orange'
                            color = 'orange'
                    else:
                        # If WMA values are NaN, default to orange
                        WMA45_current_color = 'orange'
                        color = 'orange'
                else:
                    color = 'cyan'  # maintain current

            elif WMA45_current_color == 'orange':
                if crossover_20_65 == 'upward':
                    WMA45_current_color = 'green'
                    color = 'green'
                elif crossover_45_65 == 'downward':
                    WMA45_current_color = 'red'
                    color = 'red'
                else:
                    color = 'orange'  # maintain current

            # Set both color code columns to the same value
            df_copy.iloc[i, df_copy.columns.get_loc('WMA45_color_code')] = color
            df_copy.iloc[i, df_copy.columns.get_loc('WMA65_color_code')] = color

    except Exception as e:
        print(f"  ✗ Error calculating WMA45/WMA65 color codes: {str(e)}")
        df_copy['WMA45_color_code'] = ''
        df_copy['WMA65_color_code'] = ''

    return df_copy


def calculate_wma225_color_code(df):
    """
    Calculate WMA225 color code based on crossover signals.

    Args:
        df (pd.DataFrame): DataFrame with crossover signal columns

    Returns:
        pd.DataFrame: DataFrame with added WMA225_color_code column
    """
    df_copy = df.copy()

    # Check if DataFrame is empty
    if df_copy.empty:
        return df_copy

    # Required crossover signal columns
    required_columns = [
        'crossover_signal_225_325',
        'crossover_signal_45_225'
    ]

    # Check if required columns exist
    missing_columns = [col for col in required_columns if col not in df_copy.columns]
    if missing_columns:
        # Initialize with empty values if columns are missing
        df_copy['WMA225_color_code'] = ''
        return df_copy

    try:
        # Initialize the WMA225_color_code column
        df_copy['WMA225_color_code'] = ''

        # Initialize tracking variable
        WMA225_current_color = None

        # Step 1: Find the first crossover_signal_225_325 to set initial color
        for i in range(len(df_copy)):
            crossover_225_325 = df_copy.iloc[i]['crossover_signal_225_325']

            # Skip empty/NaN values
            if pd.isna(crossover_225_325) or crossover_225_325 == ' ' or crossover_225_325 == '':
                continue

            # Set initial color based on first crossover
            if crossover_225_325 == 'downward':
                WMA225_current_color = 'red'
                df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'red'
                break
            elif crossover_225_325 == 'upward':
                WMA225_current_color = 'green'
                df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'green'
                break

        # If no initial crossover found, return with empty color codes
        if WMA225_current_color is None:
            return df_copy

        # Step 2: Process subsequent rows
        start_index = None
        for i in range(len(df_copy)):
            if df_copy.iloc[i]['WMA225_color_code'] != '':
                start_index = i + 1
                break

        if start_index is None or start_index >= len(df_copy):
            return df_copy

        for i in range(start_index, len(df_copy)):
            row = df_copy.iloc[i]

            # Get crossover signals for current row
            crossover_225_325 = row['crossover_signal_225_325']
            crossover_45_225 = row['crossover_signal_45_225']

            # Clean up crossover values (treat NaN, empty string, and space as no signal)
            def clean_signal(signal):
                if pd.isna(signal) or signal == ' ' or signal == '':
                    return None
                return signal

            crossover_225_325 = clean_signal(crossover_225_325)
            crossover_45_225 = clean_signal(crossover_45_225)

            # Apply color logic based on current color state
            if WMA225_current_color == 'red':
                if crossover_45_225 == 'upward':
                    WMA225_current_color = 'cyan'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'cyan'
                else:
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'red'

            elif WMA225_current_color == 'green':
                if crossover_45_225 == 'downward':
                    WMA225_current_color = 'orange'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'orange'
                else:
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'green'

            elif WMA225_current_color == 'cyan':
                if crossover_45_225 == 'downward':
                    WMA225_current_color = 'red'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'red'
                elif crossover_225_325 == 'upward':
                    WMA225_current_color = 'green'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'green'
                else:
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'cyan'

            elif WMA225_current_color == 'orange':
                if crossover_45_225 == 'upward':
                    WMA225_current_color = 'green'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'green'
                elif crossover_225_325 == 'downward':
                    WMA225_current_color = 'red'
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'red'
                else:
                    df_copy.iloc[i, df_copy.columns.get_loc('WMA225_color_code')] = 'orange'

    except Exception as e:
        print(f"  ✗ Error calculating WMA225 color code: {str(e)}")
        df_copy['WMA225_color_code'] = ''

    return df_copy


def process_dataframe(df):
    """
    Processes a DataFrame with WMA calculations, crossover, zone detection, and zone status tracking.

    Args:
        df (pd.DataFrame): Input dataframe with price data.

    Returns:
        pd.DataFrame: Processed DataFrame with zone status tracking.
    """
    if df is None or df.empty:
        #print("  ✗ Input DataFrame is empty or None.")
        return None

    #print(f"  → Processing DataFrame with {len(df)} rows...")

    # Calculate WMAs
    df_processed = calculate_wma(df)
    
    #Calculate RSI and ATR
    df_processed = calculate_rsi_atr(df_processed)

    # Detect crossovers 
    # Detect supply and demand zones
    # Validate zones and track their status

    df_processed = detect_crossovers(df_processed, 'WMA5', 'WMA9')
    #df_processed = detect_supply_demand_zones(df_processed, 'crossover_signal_5_9')
    #df_processed = validate_zones(df_processed, 'Zones_sup_dem_5_9')
    
    df_processed = detect_crossovers(df_processed, 'WMA5', 'WMA13')
    df_processed = detect_supply_demand_zones(df_processed, 'crossover_signal')
    df_processed = validate_zones(df_processed, 'Zones_sup_dem')
    
    
    #Update Supply and demand Zones   
    #df_processed, Supply_Zone_affected_5_9 = update_supply_zone_status(df_processed, 'crossover_signal_5_9', 'Zones_sup_dem_5_9', 'Zone_Status_5_9')
    df_processed, Supply_Zone_affected = update_supply_zone_status(df_processed, 'crossover_signal', 'Zones_sup_dem', 'Zone_Status')
    #df_processed, Supply_Zone_affected_45_65 = update_supply_zone_status(df_processed, 'crossover_signal_45_65', 'Zones_sup_dem_45_65', 'Zone_Status_45_65')

    #df_processed, Demand_Zone_affected_5_9 = update_demand_zone_status(df_processed, 'crossover_signal_5_9', 'Zones_sup_dem_5_9', 'Zone_Status_5_9')
    df_processed, Demand_Zone_affected = update_demand_zone_status(df_processed, 'crossover_signal', 'Zones_sup_dem', 'Zone_Status')
    #df_processed, Demand_Zone_affected_45_65 = update_demand_zone_status(df_processed, 'crossover_signal_45_65', 'Zones_sup_dem_45_65', 'Zone_Status_45_65')

    df_processed = detect_crossovers(df_processed, 'WMA9', 'WMA20')
    df_processed = detect_crossovers(df_processed, 'WMA9', 'WMA26')
    df_processed = detect_crossovers(df_processed, 'WMA9', 'WMA45')
    df_processed = detect_crossovers(df_processed, 'WMA9', 'WMA65')
    df_processed = detect_crossovers(df_processed, 'WMA20', 'WMA65')
    df_processed = detect_crossovers(df_processed, 'WMA45', 'WMA65')
    #df_processed = detect_supply_demand_zones(df_processed, 'crossover_signal_45_65')
    #df_processed = validate_zones(df_processed, 'Zones_sup_dem_45_65')
    df_processed = detect_crossovers(df_processed, 'WMA9', 'WMA90')
    df_processed = detect_crossovers(df_processed, 'WMA45', 'WMA225')
    df_processed = detect_crossovers(df_processed, 'WMA225', 'WMA325')

    # Calculate WMA9 color code after all crossover signals are calculated
    df_processed = calculate_wma9_color_code(df_processed)

    # Calculate WMA45 and WMA65 color codes after all crossover signals and WMA calculations are complete
    df_processed = calculate_wma45_wma65_color_code(df_processed)

    # Calculate WMA225 color code after all crossover signals are calculated
    df_processed = calculate_wma225_color_code(df_processed)
    
    # Concatenate affected zones, handling empty DataFrames
    if not Supply_Zone_affected.empty and not Demand_Zone_affected.empty:
        Zones_affected = pd.concat([Supply_Zone_affected, Demand_Zone_affected])
        logging.info(f"  → Both supply and demand zones affected by latest candle {df_processed['timestamp'].iloc[-1]}")
    elif not Supply_Zone_affected.empty:
        Zones_affected = Supply_Zone_affected
        logging.info(f"  → Only Supply zones affected by latest candle {df_processed['timestamp'].iloc[-1]}")
    elif not Demand_Zone_affected.empty:
        Zones_affected = Demand_Zone_affected
        logging.info(f"  → Only Demand zones affected by latest candle {df_processed['timestamp'].iloc[-1]}")
    else:
        Zones_affected = pd.DataFrame()
        logging.info(f"  → No zones affected by latest candle {df_processed['timestamp'].iloc[-1]}")

    #print("  ✓ DataFrame processing complete.")
    return df_processed, Zones_affected

