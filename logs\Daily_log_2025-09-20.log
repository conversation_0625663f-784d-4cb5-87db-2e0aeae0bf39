2025-09-20 03:48:59 INFO: === ****************************************************** ===
2025-09-20 03:48:59 INFO: === ****************START LOGGING* (03:48:59) ************ ===
2025-09-20 03:48:59 INFO: === ****************************************************** ===
2025-09-20 03:48:59 INFO: === Algo Trading Bot Started ===
2025-09-20 03:48:59 INFO: Output for processed NIFTY data: processed_files
2025-09-20 03:48:59 INFO: Output for processed Options data: processed_options_files
2025-09-20 03:48:59 INFO: Options input file: User_options_input.csv
2025-09-20 03:48:59 INFO: ============================================================
2025-09-20 03:48:59 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 03:48:59 INFO: --- Processing Historical NIFTY Data (03:48:59) ---
2025-09-20 03:48:59 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 03:48:59 INFO:  Fetching 13 dailymin data...
2025-09-20 03:48:59 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 03:48:59 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_daily.csv
2025-09-20 03:48:59 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00 for NIFTY for interval daily
2025-09-20 03:48:59 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00
2025-09-20 03:48:59 INFO:  Fetched 250 new records
2025-09-20 03:48:59 INFO:   \u2192 No zones affected by latest candle 2025-09-19 09:15:00
2025-09-20 03:48:59 INFO:  Saved processed file: NIFTY_daily_2024-09-20_to_2025-09-19_processed.csv
2025-09-20 03:48:59 INFO:  Fetching 13 1min data...
2025-09-20 03:48:59 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 03:48:59 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1min.csv
2025-09-20 03:48:59 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 1
2025-09-20 03:48:59 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:48:59 INFO:  Fetched 3381 new records
2025-09-20 03:49:00 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:49:00 INFO: Fresh data not coming in for Index. Please check Websocket connection
2025-09-20 03:50:19 INFO: === ****************************************************** ===
2025-09-20 03:50:19 INFO: === ****************START LOGGING* (03:50:19) ************ ===
2025-09-20 03:50:19 INFO: === ****************************************************** ===
2025-09-20 03:50:19 INFO: === Algo Trading Bot Started ===
2025-09-20 03:50:19 INFO: Output for processed NIFTY data: processed_files
2025-09-20 03:50:19 INFO: Output for processed Options data: processed_options_files
2025-09-20 03:50:19 INFO: Options input file: User_options_input.csv
2025-09-20 03:50:19 INFO: ============================================================
2025-09-20 03:50:19 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 03:50:19 INFO: --- Processing Historical NIFTY Data (03:50:19) ---
2025-09-20 03:50:19 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 03:50:19 INFO:  Fetching 13 5min data...
2025-09-20 03:50:19 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 03:50:19 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_5min.csv
2025-09-20 03:50:19 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 5
2025-09-20 03:50:19 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:50:19 INFO:  Fetched 1127 new records
2025-09-20 03:50:19 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:50:19 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 03:50:19 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 03:50:19 INFO:  Fetching 13 1min data...
2025-09-20 03:50:19 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 03:50:19 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1min.csv
2025-09-20 03:50:19 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 1
2025-09-20 03:50:19 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:50:19 INFO:  Fetched 3381 new records
2025-09-20 03:50:20 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:50:20 INFO: Fresh data not coming in for Index. Please check Websocket connection
2025-09-20 03:54:42 INFO: === ****************************************************** ===
2025-09-20 03:54:42 INFO: === ****************START LOGGING* (03:54:42) ************ ===
2025-09-20 03:54:42 INFO: === ****************************************************** ===
2025-09-20 03:54:42 INFO: === Algo Trading Bot Started ===
2025-09-20 03:54:42 INFO: Output for processed NIFTY data: processed_files
2025-09-20 03:54:42 INFO: Output for processed Options data: processed_options_files
2025-09-20 03:54:42 INFO: Options input file: User_options_input.csv
2025-09-20 03:54:42 INFO: ============================================================
2025-09-20 03:54:42 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 03:54:42 INFO: --- Processing Historical NIFTY Data (03:54:42) ---
2025-09-20 03:54:42 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 03:54:42 INFO:  Fetching 13 60min data...
2025-09-20 03:54:42 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-20 03:54:42 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1hour.csv
2025-09-20 03:54:42 INFO: Data fetched from 2025-07-21 09:15:00 to 2025-09-19 15:15:00 for NIFTY for interval 60
2025-09-20 03:54:42 INFO: Data fetched from 2025-07-21 09:15:00 to 2025-09-19 15:15:00
2025-09-20 03:54:42 INFO:  Fetched 301 new records
2025-09-20 03:54:42 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:15:00
2025-09-20 03:54:42 INFO:  Saved processed file: NIFTY_60min_2025-07-21_to_2025-09-19_processed.csv
2025-09-20 03:54:42 INFO:  Fetching 13 1min data...
2025-09-20 03:54:42 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 03:54:42 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1min.csv
2025-09-20 03:54:42 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 1
2025-09-20 03:54:42 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:54:42 INFO:  Fetched 3381 new records
2025-09-20 03:54:43 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:54:43 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-20 03:54:43 INFO:  Saved processed file: NIFTY_1min_2025-09-09_to_2025-09-19_processed.csv
2025-09-20 03:54:43 INFO:  Fetching 13 dailymin data...
2025-09-20 03:54:43 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 03:54:43 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_daily.csv
2025-09-20 03:54:43 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00 for NIFTY for interval daily
2025-09-20 03:54:43 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00
2025-09-20 03:54:43 INFO:  Fetched 250 new records
2025-09-20 03:54:43 INFO:   \u2192 No zones affected by latest candle 2025-09-19 09:15:00
2025-09-20 03:54:43 INFO:  Saved processed file: NIFTY_daily_2024-09-20_to_2025-09-19_processed.csv
2025-09-20 03:54:43 INFO:  Fetching 13 5min data...
2025-09-20 03:54:43 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 03:54:43 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_5min.csv
2025-09-20 03:54:43 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 5
2025-09-20 03:54:43 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:54:43 INFO:  Fetched 1127 new records
2025-09-20 03:54:43 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:54:43 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 03:54:43 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 03:54:43 INFO: Resistant zone: Supply low=25315.75, high=25360.4, status=Tested
2025-09-20 03:54:43 INFO: ============================================================
2025-09-20 03:54:43 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-20 03:54:43 INFO: ============================================================

2025-09-20 03:54:43 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
2025-09-20 05:12:16 INFO: === ****************************************************** ===
2025-09-20 05:12:16 INFO: === ****************START LOGGING* (05:12:16) ************ ===
2025-09-20 05:12:16 INFO: === ****************************************************** ===
2025-09-20 05:12:16 INFO: === Algo Trading Bot Started ===
2025-09-20 05:12:16 INFO: Output for processed NIFTY data: processed_files
2025-09-20 05:12:16 INFO: Output for processed Options data: processed_options_files
2025-09-20 05:12:16 INFO: Options input file: User_options_input.csv
2025-09-20 05:12:16 INFO: ============================================================
2025-09-20 05:12:16 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 05:12:16 INFO: --- Processing Historical NIFTY Data (05:12:16) ---
2025-09-20 05:12:16 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 05:12:16 INFO:  Fetching 13 1min data...
2025-09-20 05:12:16 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 05:12:16 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1min.csv
2025-09-20 05:12:16 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 1
2025-09-20 05:12:16 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00
2025-09-20 05:12:16 INFO:  Fetched 3381 new records
2025-09-20 05:12:16 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 05:12:16 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-20 05:12:16 INFO:  Saved processed file: NIFTY_1min_2025-09-09_to_2025-09-19_processed.csv
2025-09-20 05:12:16 INFO:  Fetching 13 60min data...
2025-09-20 05:12:16 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-20 05:12:16 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1hour.csv
2025-09-20 05:12:16 INFO: Data fetched from 2025-07-21 09:15:00 to 2025-09-19 15:15:00 for NIFTY for interval 60
2025-09-20 05:12:16 INFO: Data fetched from 2025-07-21 09:15:00 to 2025-09-19 15:15:00
2025-09-20 05:12:16 INFO:  Fetched 301 new records
2025-09-20 05:12:16 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:15:00
2025-09-20 05:12:16 INFO:  Saved processed file: NIFTY_60min_2025-07-21_to_2025-09-19_processed.csv
2025-09-20 05:12:16 INFO:  Fetching 13 dailymin data...
2025-09-20 05:12:16 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 05:12:16 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_daily.csv
2025-09-20 05:12:16 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00 for NIFTY for interval daily
2025-09-20 05:12:16 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00
2025-09-20 05:12:16 INFO:  Fetched 250 new records
2025-09-20 05:12:17 INFO:   \u2192 No zones affected by latest candle 2025-09-19 09:15:00
2025-09-20 05:12:17 INFO:  Saved processed file: NIFTY_daily_2024-09-20_to_2025-09-19_processed.csv
2025-09-20 05:12:17 INFO:  Fetching 13 5min data...
2025-09-20 05:12:17 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 05:12:17 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_5min.csv
2025-09-20 05:12:17 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 5
2025-09-20 05:12:17 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00
2025-09-20 05:12:17 INFO:  Fetched 1127 new records
2025-09-20 05:12:17 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 05:12:17 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 05:12:17 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 05:12:17 INFO: Resistant zone: Supply low=25315.75, high=25360.4, status=Tested
2025-09-20 05:12:17 INFO: ============================================================
2025-09-20 05:12:17 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-20 05:12:17 INFO: ============================================================

2025-09-20 05:12:17 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
