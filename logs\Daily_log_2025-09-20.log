2025-09-20 03:48:59 INFO: === ****************************************************** ===
2025-09-20 03:48:59 INFO: === ****************START LOGGING* (03:48:59) ************ ===
2025-09-20 03:48:59 INFO: === ****************************************************** ===
2025-09-20 03:48:59 INFO: === Algo Trading Bot Started ===
2025-09-20 03:48:59 INFO: Output for processed NIFTY data: processed_files
2025-09-20 03:48:59 INFO: Output for processed Options data: processed_options_files
2025-09-20 03:48:59 INFO: Options input file: User_options_input.csv
2025-09-20 03:48:59 INFO: ============================================================
2025-09-20 03:48:59 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 03:48:59 INFO: --- Processing Historical NIFTY Data (03:48:59) ---
2025-09-20 03:48:59 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 03:48:59 INFO:  Fetching 13 dailymin data...
2025-09-20 03:48:59 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 03:48:59 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_daily.csv
2025-09-20 03:48:59 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00 for NIFTY for interval daily
2025-09-20 03:48:59 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00
2025-09-20 03:48:59 INFO:  Fetched 250 new records
2025-09-20 03:48:59 INFO:   \u2192 No zones affected by latest candle 2025-09-19 09:15:00
2025-09-20 03:48:59 INFO:  Saved processed file: NIFTY_daily_2024-09-20_to_2025-09-19_processed.csv
2025-09-20 03:48:59 INFO:  Fetching 13 1min data...
2025-09-20 03:48:59 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 03:48:59 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1min.csv
2025-09-20 03:48:59 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 1
2025-09-20 03:48:59 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:48:59 INFO:  Fetched 3381 new records
2025-09-20 03:49:00 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:49:00 INFO: Fresh data not coming in for Index. Please check Websocket connection
2025-09-20 03:50:19 INFO: === ****************************************************** ===
2025-09-20 03:50:19 INFO: === ****************START LOGGING* (03:50:19) ************ ===
2025-09-20 03:50:19 INFO: === ****************************************************** ===
2025-09-20 03:50:19 INFO: === Algo Trading Bot Started ===
2025-09-20 03:50:19 INFO: Output for processed NIFTY data: processed_files
2025-09-20 03:50:19 INFO: Output for processed Options data: processed_options_files
2025-09-20 03:50:19 INFO: Options input file: User_options_input.csv
2025-09-20 03:50:19 INFO: ============================================================
2025-09-20 03:50:19 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 03:50:19 INFO: --- Processing Historical NIFTY Data (03:50:19) ---
2025-09-20 03:50:19 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 03:50:19 INFO:  Fetching 13 5min data...
2025-09-20 03:50:19 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 03:50:19 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_5min.csv
2025-09-20 03:50:19 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 5
2025-09-20 03:50:19 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:50:19 INFO:  Fetched 1127 new records
2025-09-20 03:50:19 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:50:19 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 03:50:19 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 03:50:19 INFO:  Fetching 13 1min data...
2025-09-20 03:50:19 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 03:50:19 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1min.csv
2025-09-20 03:50:19 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 1
2025-09-20 03:50:19 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:50:19 INFO:  Fetched 3381 new records
2025-09-20 03:50:20 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:50:20 INFO: Fresh data not coming in for Index. Please check Websocket connection
2025-09-20 03:54:42 INFO: === ****************************************************** ===
2025-09-20 03:54:42 INFO: === ****************START LOGGING* (03:54:42) ************ ===
2025-09-20 03:54:42 INFO: === ****************************************************** ===
2025-09-20 03:54:42 INFO: === Algo Trading Bot Started ===
2025-09-20 03:54:42 INFO: Output for processed NIFTY data: processed_files
2025-09-20 03:54:42 INFO: Output for processed Options data: processed_options_files
2025-09-20 03:54:42 INFO: Options input file: User_options_input.csv
2025-09-20 03:54:42 INFO: ============================================================
2025-09-20 03:54:42 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 03:54:42 INFO: --- Processing Historical NIFTY Data (03:54:42) ---
2025-09-20 03:54:42 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 03:54:42 INFO:  Fetching 13 60min data...
2025-09-20 03:54:42 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-20 03:54:42 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1hour.csv
2025-09-20 03:54:42 INFO: Data fetched from 2025-07-21 09:15:00 to 2025-09-19 15:15:00 for NIFTY for interval 60
2025-09-20 03:54:42 INFO: Data fetched from 2025-07-21 09:15:00 to 2025-09-19 15:15:00
2025-09-20 03:54:42 INFO:  Fetched 301 new records
2025-09-20 03:54:42 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:15:00
2025-09-20 03:54:42 INFO:  Saved processed file: NIFTY_60min_2025-07-21_to_2025-09-19_processed.csv
2025-09-20 03:54:42 INFO:  Fetching 13 1min data...
2025-09-20 03:54:42 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 03:54:42 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1min.csv
2025-09-20 03:54:42 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 1
2025-09-20 03:54:42 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:54:42 INFO:  Fetched 3381 new records
2025-09-20 03:54:43 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:54:43 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-20 03:54:43 INFO:  Saved processed file: NIFTY_1min_2025-09-09_to_2025-09-19_processed.csv
2025-09-20 03:54:43 INFO:  Fetching 13 dailymin data...
2025-09-20 03:54:43 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 03:54:43 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_daily.csv
2025-09-20 03:54:43 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00 for NIFTY for interval daily
2025-09-20 03:54:43 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00
2025-09-20 03:54:43 INFO:  Fetched 250 new records
2025-09-20 03:54:43 INFO:   \u2192 No zones affected by latest candle 2025-09-19 09:15:00
2025-09-20 03:54:43 INFO:  Saved processed file: NIFTY_daily_2024-09-20_to_2025-09-19_processed.csv
2025-09-20 03:54:43 INFO:  Fetching 13 5min data...
2025-09-20 03:54:43 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 03:54:43 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_5min.csv
2025-09-20 03:54:43 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 5
2025-09-20 03:54:43 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00
2025-09-20 03:54:43 INFO:  Fetched 1127 new records
2025-09-20 03:54:43 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 03:54:43 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 03:54:43 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 03:54:43 INFO: Resistant zone: Supply low=25315.75, high=25360.4, status=Tested
2025-09-20 03:54:43 INFO: ============================================================
2025-09-20 03:54:43 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-20 03:54:43 INFO: ============================================================

2025-09-20 03:54:43 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
2025-09-20 05:12:16 INFO: === ****************************************************** ===
2025-09-20 05:12:16 INFO: === ****************START LOGGING* (05:12:16) ************ ===
2025-09-20 05:12:16 INFO: === ****************************************************** ===
2025-09-20 05:12:16 INFO: === Algo Trading Bot Started ===
2025-09-20 05:12:16 INFO: Output for processed NIFTY data: processed_files
2025-09-20 05:12:16 INFO: Output for processed Options data: processed_options_files
2025-09-20 05:12:16 INFO: Options input file: User_options_input.csv
2025-09-20 05:12:16 INFO: ============================================================
2025-09-20 05:12:16 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 05:12:16 INFO: --- Processing Historical NIFTY Data (05:12:16) ---
2025-09-20 05:12:16 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 05:12:16 INFO:  Fetching 13 1min data...
2025-09-20 05:12:16 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 05:12:16 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1min.csv
2025-09-20 05:12:16 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 1
2025-09-20 05:12:16 INFO: Data fetched from 2025-09-09 09:15:00 to 2025-09-19 15:35:00
2025-09-20 05:12:16 INFO:  Fetched 3381 new records
2025-09-20 05:12:16 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 05:12:16 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-20 05:12:16 INFO:  Saved processed file: NIFTY_1min_2025-09-09_to_2025-09-19_processed.csv
2025-09-20 05:12:16 INFO:  Fetching 13 60min data...
2025-09-20 05:12:16 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-20 05:12:16 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_1hour.csv
2025-09-20 05:12:16 INFO: Data fetched from 2025-07-21 09:15:00 to 2025-09-19 15:15:00 for NIFTY for interval 60
2025-09-20 05:12:16 INFO: Data fetched from 2025-07-21 09:15:00 to 2025-09-19 15:15:00
2025-09-20 05:12:16 INFO:  Fetched 301 new records
2025-09-20 05:12:16 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:15:00
2025-09-20 05:12:16 INFO:  Saved processed file: NIFTY_60min_2025-07-21_to_2025-09-19_processed.csv
2025-09-20 05:12:16 INFO:  Fetching 13 dailymin data...
2025-09-20 05:12:16 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 05:12:16 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_daily.csv
2025-09-20 05:12:16 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00 for NIFTY for interval daily
2025-09-20 05:12:16 INFO: Data fetched from 2024-09-20 00:00:00 to 2025-09-19 09:15:00
2025-09-20 05:12:16 INFO:  Fetched 250 new records
2025-09-20 05:12:17 INFO:   \u2192 No zones affected by latest candle 2025-09-19 09:15:00
2025-09-20 05:12:17 INFO:  Saved processed file: NIFTY_daily_2024-09-20_to_2025-09-19_processed.csv
2025-09-20 05:12:17 INFO:  Fetching 13 5min data...
2025-09-20 05:12:17 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 05:12:17 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-19\NIFTY\nifty_candles_5min.csv
2025-09-20 05:12:17 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00 for NIFTY for interval 5
2025-09-20 05:12:17 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:35:00
2025-09-20 05:12:17 INFO:  Fetched 1127 new records
2025-09-20 05:12:17 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:35:00
2025-09-20 05:12:17 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 05:12:17 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 05:12:17 INFO: Resistant zone: Supply low=25315.75, high=25360.4, status=Tested
2025-09-20 05:12:17 INFO: ============================================================
2025-09-20 05:12:17 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-20 05:12:17 INFO: ============================================================

2025-09-20 05:12:17 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
2025-09-20 18:15:41 INFO: === ****************************************************** ===
2025-09-20 18:15:41 INFO: === ****************START LOGGING* (18:15:41) ************ ===
2025-09-20 18:15:41 INFO: === ****************************************************** ===
2025-09-20 18:15:41 INFO: === Algo Trading Bot Started ===
2025-09-20 18:15:41 INFO: Output for processed NIFTY data: processed_files
2025-09-20 18:15:41 INFO: Output for processed Options data: processed_options_files
2025-09-20 18:15:41 INFO: Options input file: User_options_input.csv
2025-09-20 18:15:41 INFO: ============================================================
2025-09-20 18:15:41 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 18:15:41 INFO: --- Processing Historical NIFTY Data (18:15:41) ---
2025-09-20 18:15:41 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 18:15:41 INFO:  Quarter-hour mark (18:15): Adding 60min fetch
2025-09-20 18:15:41 INFO:  Fetching 13 1min data...
2025-09-20 18:15:41 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 18:15:41 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1min.csv
2025-09-20 18:15:43 ERROR: Error reading CSV file C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1min.csv: File does not exist: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1min.csv
2025-09-20 18:18:47 INFO: === ****************************************************** ===
2025-09-20 18:18:47 INFO: === ****************START LOGGING* (18:18:47) ************ ===
2025-09-20 18:18:47 INFO: === ****************************************************** ===
2025-09-20 18:18:47 INFO: === Algo Trading Bot Started ===
2025-09-20 18:18:47 INFO: Output for processed NIFTY data: processed_files
2025-09-20 18:18:47 INFO: Output for processed Options data: processed_options_files
2025-09-20 18:18:47 INFO: Options input file: User_options_input.csv
2025-09-20 18:18:47 INFO: ============================================================
2025-09-20 18:18:47 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 18:18:47 INFO: --- Processing Historical NIFTY Data (18:18:47) ---
2025-09-20 18:18:47 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 18:18:47 INFO:  Fetching 13 5min data...
2025-09-20 18:18:47 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 18:18:47 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_5min.csv
2025-09-20 18:18:47 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:25:00 for NIFTY for interval 5
2025-09-20 18:18:47 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:25:00
2025-09-20 18:18:47 INFO:  Fetched 1125 new records
2025-09-20 18:18:47 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:25:00
2025-09-20 18:18:47 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 18:18:47 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 18:18:47 INFO:  Fetching 13 1min data...
2025-09-20 18:18:47 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 18:18:47 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1min.csv
2025-09-20 18:18:47 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-19 15:29:00 for NIFTY for interval 1
2025-09-20 18:18:47 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-19 15:29:00
2025-09-20 18:18:47 INFO:  Fetched 3000 new records
2025-09-20 18:18:49 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:29:00
2025-09-20 18:18:49 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-20 18:18:49 INFO:  Saved processed file: NIFTY_1min_2025-09-10_to_2025-09-19_processed.csv
2025-09-20 18:18:49 INFO:  Fetching 13 60min data...
2025-09-20 18:18:49 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-20 18:18:49 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1hour.csv
2025-09-20 18:18:49 INFO: Data fetched from 2025-07-22 09:15:00 to 2025-09-19 15:15:00 for NIFTY for interval 60
2025-09-20 18:18:49 INFO: Data fetched from 2025-07-22 09:15:00 to 2025-09-19 15:15:00
2025-09-20 18:18:49 INFO:  Fetched 294 new records
2025-09-20 18:18:49 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:15:00
2025-09-20 18:18:49 INFO:  Saved processed file: NIFTY_60min_2025-07-22_to_2025-09-19_processed.csv
2025-09-20 18:18:49 INFO:  Fetching 13 dailymin data...
2025-09-20 18:18:49 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 18:18:49 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_daily.csv
2025-09-20 18:18:49 INFO: Data fetched from 2024-09-23 00:00:00 to 2025-09-19 00:00:00 for NIFTY for interval daily
2025-09-20 18:18:49 INFO: Data fetched from 2024-09-23 00:00:00 to 2025-09-19 00:00:00
2025-09-20 18:18:49 INFO:  Fetched 248 new records
2025-09-20 18:18:49 INFO:   \u2192 No zones affected by latest candle 2025-09-19 00:00:00
2025-09-20 18:18:49 INFO:  Saved processed file: NIFTY_daily_2024-09-23_to_2025-09-19_processed.csv
2025-09-20 18:18:49 INFO: Resistant zone: Supply low=25315.75, high=25360.4, status=Tested
2025-09-20 18:18:49 INFO: ============================================================
2025-09-20 18:18:49 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-20 18:18:49 INFO: ============================================================

2025-09-20 18:18:49 INFO: Market is closed (current time: 18:18:49.642948). Exiting...
2025-09-20 18:56:47 INFO: === ****************************************************** ===
2025-09-20 18:56:47 INFO: === ****************START LOGGING* (18:56:47) ************ ===
2025-09-20 18:56:47 INFO: === ****************************************************** ===
2025-09-20 18:56:47 INFO: === Algo Trading Bot Started ===
2025-09-20 18:56:47 INFO: Output for processed NIFTY data: processed_files
2025-09-20 18:56:47 INFO: Output for processed Options data: processed_options_files
2025-09-20 18:56:47 INFO: Options input file: User_options_input.csv
2025-09-20 18:56:47 INFO: ============================================================
2025-09-20 18:56:47 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 18:56:47 INFO: --- Processing Historical NIFTY Data (18:56:47) ---
2025-09-20 18:56:47 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 18:56:47 INFO:  Fetching 13 dailymin data...
2025-09-20 18:56:47 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 18:56:47 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_daily.csv
2025-09-20 18:56:47 INFO: Data fetched from 2024-09-23 00:00:00 to 2025-09-19 00:00:00 for NIFTY for interval daily
2025-09-20 18:56:47 INFO: Data fetched from 2024-09-23 00:00:00 to 2025-09-19 00:00:00
2025-09-20 18:56:47 INFO:  Fetched 248 new records
2025-09-20 18:56:47 INFO:   \u2192 No zones affected by latest candle 2025-09-19 00:00:00
2025-09-20 18:56:47 INFO:  Saved processed file: NIFTY_daily_2024-09-23_to_2025-09-19_processed.csv
2025-09-20 18:56:47 INFO:  Fetching 13 5min data...
2025-09-20 18:56:47 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 18:56:47 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_5min.csv
2025-09-20 18:56:47 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:25:00 for NIFTY for interval 5
2025-09-20 18:56:47 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:25:00
2025-09-20 18:56:47 INFO:  Fetched 1125 new records
2025-09-20 18:56:48 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:25:00
2025-09-20 18:56:48 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 18:56:48 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 18:56:48 INFO:  Fetching 13 60min data...
2025-09-20 18:56:48 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-20 18:56:48 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1hour.csv
2025-09-20 18:56:48 INFO: Data fetched from 2025-07-22 09:15:00 to 2025-09-19 15:15:00 for NIFTY for interval 60
2025-09-20 18:56:48 INFO: Data fetched from 2025-07-22 09:15:00 to 2025-09-19 15:15:00
2025-09-20 18:56:48 INFO:  Fetched 294 new records
2025-09-20 18:56:48 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:15:00
2025-09-20 18:56:48 INFO:  Saved processed file: NIFTY_60min_2025-07-22_to_2025-09-19_processed.csv
2025-09-20 18:56:48 INFO:  Fetching 13 1min data...
2025-09-20 18:56:48 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 18:56:48 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1min.csv
2025-09-20 18:56:48 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-19 15:29:00 for NIFTY for interval 1
2025-09-20 18:56:48 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-19 15:29:00
2025-09-20 18:56:48 INFO:  Fetched 3000 new records
2025-09-20 18:56:50 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:29:00
2025-09-20 18:56:50 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-20 18:56:50 INFO:  Saved processed file: NIFTY_1min_2025-09-10_to_2025-09-19_processed.csv
2025-09-20 18:56:50 INFO: Resistant zone: Supply low=25315.75, high=25360.4, status=Tested
2025-09-20 18:56:50 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-09-20 18:56:50 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL
2025-09-20 18:56:50 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL\nifty_23_sep_25400_call_candles_1min.csv for interval 1
2025-09-20 18:56:50 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-20 13:49:00 for NIFTY 23 SEP 25400 CALL for interval 1
2025-09-20 18:56:50 ERROR: Error reading CSV file C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL\nifty_23_sep_25400_call_candles_1min.csv: type object 'datetime.datetime' has no attribute 'datetime'
2025-09-20 18:56:50 ERROR: Error during options processing: Error reading CSV file: type object 'datetime.datetime' has no attribute 'datetime'
2025-09-20 18:56:50 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-09-20 18:56:50 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT
2025-09-20 18:56:50 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT\nifty_23_sep_25400_put_candles_1min.csv for interval 1
2025-09-20 18:56:50 INFO: Data fetched from 2025-09-10 09:16:00 to 2025-09-20 13:05:00 for NIFTY 23 SEP 25400 PUT for interval 1
2025-09-20 18:56:50 ERROR: Error reading CSV file C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT\nifty_23_sep_25400_put_candles_1min.csv: type object 'datetime.datetime' has no attribute 'datetime'
2025-09-20 18:56:50 ERROR: Error during options processing: Error reading CSV file: type object 'datetime.datetime' has no attribute 'datetime'
2025-09-20 18:56:50 INFO: ============================================================
2025-09-20 18:56:50 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-20 18:56:50 INFO: ============================================================

2025-09-20 18:56:50 INFO: Market is closed (current time: 18:56:50.124628). Exiting...
2025-09-20 19:03:26 INFO: === ****************************************************** ===
2025-09-20 19:03:26 INFO: === ****************START LOGGING* (19:03:26) ************ ===
2025-09-20 19:03:26 INFO: === ****************************************************** ===
2025-09-20 19:03:26 INFO: === Algo Trading Bot Started ===
2025-09-20 19:03:26 INFO: Output for processed NIFTY data: processed_files
2025-09-20 19:03:26 INFO: Output for processed Options data: processed_options_files
2025-09-20 19:03:26 INFO: Options input file: User_options_input.csv
2025-09-20 19:03:26 INFO: ============================================================
2025-09-20 19:03:26 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 19:03:26 INFO: --- Processing Historical NIFTY Data (19:03:26) ---
2025-09-20 19:03:26 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 19:03:26 INFO:  Fetching 13 dailymin data...
2025-09-20 19:03:26 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 19:03:26 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_daily.csv
2025-09-20 19:03:26 INFO: Data fetched from 2024-09-23 00:00:00 to 2025-09-19 00:00:00 for NIFTY for interval daily
2025-09-20 19:03:26 INFO: Data fetched from 2024-09-23 00:00:00 to 2025-09-19 00:00:00
2025-09-20 19:03:26 INFO:  Fetched 248 new records
2025-09-20 19:03:26 INFO:   \u2192 No zones affected by latest candle 2025-09-19 00:00:00
2025-09-20 19:03:26 INFO:  Saved processed file: NIFTY_daily_2024-09-23_to_2025-09-19_processed.csv
2025-09-20 19:03:26 INFO:  Fetching 13 5min data...
2025-09-20 19:03:26 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 19:03:26 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_5min.csv
2025-09-20 19:03:26 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:25:00 for NIFTY for interval 5
2025-09-20 19:03:26 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:25:00
2025-09-20 19:03:26 INFO:  Fetched 1125 new records
2025-09-20 19:03:27 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:25:00
2025-09-20 19:03:27 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 19:03:27 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 19:03:27 INFO:  Fetching 13 1min data...
2025-09-20 19:03:27 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 19:03:27 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1min.csv
2025-09-20 19:03:27 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-19 15:29:00 for NIFTY for interval 1
2025-09-20 19:03:27 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-19 15:29:00
2025-09-20 19:03:27 INFO:  Fetched 3000 new records
2025-09-20 19:03:28 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:29:00
2025-09-20 19:03:28 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-20 19:03:29 INFO:  Saved processed file: NIFTY_1min_2025-09-10_to_2025-09-19_processed.csv
2025-09-20 19:03:29 INFO:  Fetching 13 60min data...
2025-09-20 19:03:29 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-20 19:03:29 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1hour.csv
2025-09-20 19:03:29 INFO: Data fetched from 2025-07-22 09:15:00 to 2025-09-19 15:15:00 for NIFTY for interval 60
2025-09-20 19:03:29 INFO: Data fetched from 2025-07-22 09:15:00 to 2025-09-19 15:15:00
2025-09-20 19:03:29 INFO:  Fetched 294 new records
2025-09-20 19:03:29 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:15:00
2025-09-20 19:03:29 INFO:  Saved processed file: NIFTY_60min_2025-07-22_to_2025-09-19_processed.csv
2025-09-20 19:03:29 INFO: Resistant zone: Supply low=25315.75, high=25360.4, status=Tested
2025-09-20 19:03:29 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-09-20 19:03:29 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL
2025-09-20 19:03:29 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL\nifty_23_sep_25400_call_candles_1min.csv for interval 1
2025-09-20 19:03:29 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-20 13:49:00 for NIFTY 23 SEP 25400 CALL for interval 1
2025-09-20 19:03:30 INFO:   \u2192 No zones affected by latest candle 2025-09-20 13:49:00
2025-09-20 19:03:30 INFO:      Saved 1min data: NIFTY_23_SEP_25400_CALL_1min_2025-09-10_to_2025-09-20_processed.csv
2025-09-20 19:03:30 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL\nifty_23_sep_25400_call_candles_5min.csv for interval 5
2025-09-20 19:03:30 INFO: Data fetched from 2025-09-02 10:05:00 to 2025-09-20 13:45:00 for NIFTY 23 SEP 25400 CALL for interval 5
2025-09-20 19:03:31 INFO:   \u2192 No zones affected by latest candle 2025-09-20 13:45:00
2025-09-20 19:03:31 INFO:      Saved 5min data: NIFTY_23_SEP_25400_CALL_5min_2025-09-02_to_2025-09-20_processed.csv
2025-09-20 19:03:31 INFO: Options processing completed: 0 successful, 0 failed
2025-09-20 19:03:31 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-09-20 19:03:31 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT
2025-09-20 19:03:31 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT\nifty_23_sep_25400_put_candles_1min.csv for interval 1
2025-09-20 19:03:31 INFO: Data fetched from 2025-09-10 09:16:00 to 2025-09-20 13:05:00 for NIFTY 23 SEP 25400 PUT for interval 1
2025-09-20 19:03:32 INFO:   \u2192 Only Demand zones affected by latest candle 2025-09-20 13:05:00
2025-09-20 19:03:32 INFO:      Saved 1min data: NIFTY_23_SEP_25400_PUT_1min_2025-09-10_to_2025-09-20_processed.csv
2025-09-20 19:03:32 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT\nifty_23_sep_25400_put_candles_5min.csv for interval 5
2025-09-20 19:03:32 INFO: Data fetched from 2025-09-04 09:20:00 to 2025-09-20 13:05:00 for NIFTY 23 SEP 25400 PUT for interval 5
2025-09-20 19:03:32 INFO:   \u2192 No zones affected by latest candle 2025-09-20 13:05:00
2025-09-20 19:03:32 INFO:      Saved 5min data: NIFTY_23_SEP_25400_PUT_5min_2025-09-04_to_2025-09-20_processed.csv
2025-09-20 19:03:32 INFO: Options processing completed: 0 successful, 0 failed
2025-09-20 19:03:32 INFO: ============================================================
2025-09-20 19:03:32 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-20 19:03:32 INFO: ============================================================

2025-09-20 19:03:32 INFO: Market is closed (current time: 19:03:29.175026). Exiting...
2025-09-20 21:50:26 INFO: === ****************************************************** ===
2025-09-20 21:50:26 INFO: === ****************START LOGGING* (21:50:26) ************ ===
2025-09-20 21:50:26 INFO: === ****************************************************** ===
2025-09-20 21:50:26 INFO: === Algo Trading Bot Started ===
2025-09-20 21:50:26 INFO: Output for processed NIFTY data: processed_files
2025-09-20 21:50:26 INFO: Output for processed Options data: processed_options_files
2025-09-20 21:50:26 INFO: Options input file: User_options_input.csv
2025-09-20 21:50:26 INFO: ============================================================
2025-09-20 21:50:26 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-20 21:50:26 INFO: --- Processing Historical NIFTY Data (21:50:26) ---
2025-09-20 21:50:26 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-20 21:50:26 INFO:  Fetching 13 60min data...
2025-09-20 21:50:26 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-20 21:50:26 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1hour.csv
2025-09-20 21:50:26 INFO: Data fetched from 2025-07-22 09:15:00 to 2025-09-19 15:15:00 for NIFTY for interval 60
2025-09-20 21:50:26 INFO: Data fetched from 2025-07-22 09:15:00 to 2025-09-19 15:15:00
2025-09-20 21:50:26 INFO:  Fetched 294 new records
2025-09-20 21:50:26 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:15:00
2025-09-20 21:50:26 INFO:  Saved processed file: NIFTY_60min_2025-07-22_to_2025-09-19_processed.csv
2025-09-20 21:50:26 INFO:  Fetching 13 dailymin data...
2025-09-20 21:50:26 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-20 21:50:26 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_daily.csv
2025-09-20 21:50:26 INFO: Data fetched from 2024-09-23 00:00:00 to 2025-09-19 00:00:00 for NIFTY for interval daily
2025-09-20 21:50:26 INFO: Data fetched from 2024-09-23 00:00:00 to 2025-09-19 00:00:00
2025-09-20 21:50:26 INFO:  Fetched 248 new records
2025-09-20 21:50:26 INFO:   \u2192 No zones affected by latest candle 2025-09-19 00:00:00
2025-09-20 21:50:26 INFO:  Saved processed file: NIFTY_daily_2024-09-23_to_2025-09-19_processed.csv
2025-09-20 21:50:26 INFO:  Fetching 13 5min data...
2025-09-20 21:50:26 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-20 21:50:26 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_5min.csv
2025-09-20 21:50:26 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:25:00 for NIFTY for interval 5
2025-09-20 21:50:26 INFO: Data fetched from 2025-09-01 09:15:00 to 2025-09-19 15:25:00
2025-09-20 21:50:26 INFO:  Fetched 1125 new records
2025-09-20 21:50:27 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:25:00
2025-09-20 21:50:27 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-20 21:50:27 INFO:  Saved processed file: NIFTY_5min_2025-09-01_to_2025-09-19_processed.csv
2025-09-20 21:50:27 INFO:  Fetching 13 1min data...
2025-09-20 21:50:27 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-20 21:50:27 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-20\NIFTY\nifty_candles_1min.csv
2025-09-20 21:50:27 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-19 15:29:00 for NIFTY for interval 1
2025-09-20 21:50:27 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-19 15:29:00
2025-09-20 21:50:27 INFO:  Fetched 3000 new records
2025-09-20 21:50:28 INFO:   \u2192 No zones affected by latest candle 2025-09-19 15:29:00
2025-09-20 21:50:28 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-20 21:50:28 INFO:  Saved processed file: NIFTY_1min_2025-09-10_to_2025-09-19_processed.csv
2025-09-20 21:50:28 INFO: Resistant zone: Supply low=25315.75, high=25360.4, status=Tested
2025-09-20 21:50:28 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-09-20 21:50:28 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL
2025-09-20 21:50:28 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL\nifty_23_sep_25400_call_candles_1min.csv for interval 1
2025-09-20 21:50:28 INFO: Data fetched from 2025-09-10 09:15:00 to 2025-09-20 13:49:00 for NIFTY 23 SEP 25400 CALL for interval 1
2025-09-20 21:50:28 ERROR: Error reading CSV file C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_CALL\nifty_23_sep_25400_call_candles_1min.csv: type object 'datetime.datetime' has no attribute 'datetime'
2025-09-20 21:50:28 ERROR: Error during options processing: Error reading CSV file: type object 'datetime.datetime' has no attribute 'datetime'
2025-09-20 21:50:28 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-09-20 21:50:28 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT
2025-09-20 21:50:28 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT\nifty_23_sep_25400_put_candles_1min.csv for interval 1
2025-09-20 21:50:28 INFO: Data fetched from 2025-09-10 09:16:00 to 2025-09-20 13:05:00 for NIFTY 23 SEP 25400 PUT for interval 1
2025-09-20 21:50:28 ERROR: Error reading CSV file C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-20\NIFTY_23_SEP_25400_PUT\nifty_23_sep_25400_put_candles_1min.csv: type object 'datetime.datetime' has no attribute 'datetime'
2025-09-20 21:50:28 ERROR: Error during options processing: Error reading CSV file: type object 'datetime.datetime' has no attribute 'datetime'
2025-09-20 21:50:28 INFO: ============================================================
2025-09-20 21:50:28 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-20 21:50:28 INFO: ============================================================

2025-09-20 21:50:28 INFO: Market is closed (current time: 21:50:28.806126). Exiting...
