2025-10-02 03:52:21 INFO: === ****************************************************** ===
2025-10-02 03:52:21 INFO: === ****************START LOGGING* (03:52:21) ************ ===
2025-10-02 03:52:21 INFO: === ****************************************************** ===
2025-10-02 03:52:21 INFO: === Algo Trading Bot Started ===
2025-10-02 03:52:21 INFO: Output for processed NIFTY data: processed_files
2025-10-02 03:52:21 INFO: Output for processed Options data: processed_options_files
2025-10-02 03:52:21 INFO: Options input file: User_options_input.csv
2025-10-02 03:52:21 INFO: ============================================================
2025-10-02 03:52:21 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-10-02 03:52:21 INFO: --- Processing Historical NIFTY Data (03:52:21) ---
2025-10-02 03:52:21 INFO:  First run: scheduling all intervals for full historical fetch
2025-10-02 03:52:21 INFO:  Fetching 13 dailymin data...
2025-10-02 03:52:21 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-10-02 03:52:21 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-10-01\NIFTY\nifty_candles_daily.csv
2025-10-02 03:52:21 INFO: Data fetched from 2024-10-03 00:00:00 to 2025-10-01 09:15:00 for NIFTY for interval daily
2025-10-02 03:52:21 INFO: Data fetched from 2024-10-03 00:00:00 to 2025-10-01 09:15:00
2025-10-02 03:52:21 INFO:  Fetched 250 new records
2025-10-02 03:52:21 INFO:   \u2192 No zones affected by latest candle 2025-10-01 09:15:00
2025-10-02 03:52:21 INFO:  Saved processed file: NIFTY_daily_2024-10-03_to_2025-10-01_processed.csv
2025-10-02 03:52:21 INFO:  Fetching 13 5min data...
2025-10-02 03:52:21 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-10-02 03:52:21 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-10-01\NIFTY\nifty_candles_5min.csv
2025-10-02 03:52:21 INFO: Data fetched from 2025-09-11 09:15:00 to 2025-10-01 15:30:00 for NIFTY for interval 5
2025-10-02 03:52:21 INFO: Data fetched from 2025-09-11 09:15:00 to 2025-10-01 15:30:00
2025-10-02 03:52:21 INFO:  Fetched 1126 new records
2025-10-02 03:52:22 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:52:22 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-10-02 03:52:22 INFO:  Saved processed file: NIFTY_5min_2025-09-11_to_2025-10-01_processed.csv
2025-10-02 03:52:22 INFO:  Fetching 13 1min data...
2025-10-02 03:52:22 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-10-02 03:52:22 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-10-01\NIFTY\nifty_candles_1min.csv
2025-10-02 03:52:22 INFO: Data fetched from 2025-09-22 09:15:00 to 2025-10-01 15:31:00 for NIFTY for interval 1
2025-10-02 03:52:22 INFO: Data fetched from 2025-09-22 09:15:00 to 2025-10-01 15:31:00
2025-10-02 03:52:22 INFO:  Fetched 3002 new records
2025-10-02 03:52:22 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:31:00
2025-10-02 03:52:22 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-10-02 03:52:22 INFO:  Saved processed file: NIFTY_1min_2025-09-22_to_2025-10-01_processed.csv
2025-10-02 03:52:22 INFO:  Fetching 13 60min data...
2025-10-02 03:52:22 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-10-02 03:52:22 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-10-01\NIFTY\nifty_candles_1hour.csv
2025-10-02 03:52:22 INFO: Data fetched from 2025-08-04 09:15:00 to 2025-10-01 15:15:00 for NIFTY for interval 60
2025-10-02 03:52:22 INFO: Data fetched from 2025-08-04 09:15:00 to 2025-10-01 15:15:00
2025-10-02 03:52:22 INFO:  Fetched 287 new records
2025-10-02 03:52:22 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:15:00
2025-10-02 03:52:22 INFO:  Saved processed file: NIFTY_60min_2025-08-04_to_2025-10-01_processed.csv
2025-10-02 03:52:22 INFO: Resistant zone: Supply low=24847.45, high=24867.85, status=Tested
2025-10-02 03:52:22 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-10-02 03:52:22 INFO: End date: 2025-10-01
2025-10-02 03:52:22 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24700_CALL
2025-10-02 03:52:22 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24700_CALL\nifty_07_oct_24700_call_candles_1min.csv for interval 1
2025-10-02 03:52:22 INFO: Data fetched from 2025-09-22 09:18:00 to 2025-10-01 15:30:00 for NIFTY 07 OCT 24700 CALL for interval 1
2025-10-02 03:52:23 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:52:23 INFO:      Saved 1min data: NIFTY_07_OCT_24700_CALL_1min_2025-09-22_to_2025-10-01_processed.csv
2025-10-02 03:52:23 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24700_CALL\nifty_07_oct_24700_call_candles_5min.csv for interval 5
2025-10-02 03:52:23 INFO: Data fetched from 2025-09-11 10:05:00 to 2025-10-01 15:30:00 for NIFTY 07 OCT 24700 CALL for interval 5
2025-10-02 03:52:23 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:52:23 INFO:      Saved 5min data: NIFTY_07_OCT_24700_CALL_5min_2025-09-11_to_2025-10-01_processed.csv
2025-10-02 03:52:23 INFO: Options processing completed: 0 successful, 0 failed
2025-10-02 03:52:23 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-10-02 03:52:23 INFO: End date: 2025-10-01
2025-10-02 03:52:23 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24600_PUT
2025-10-02 03:52:23 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24600_PUT\nifty_07_oct_24600_put_candles_1min.csv for interval 1
2025-10-02 03:52:23 INFO: Data fetched from 2025-09-22 09:15:00 to 2025-10-01 15:30:00 for NIFTY 07 OCT 24600 PUT for interval 1
2025-10-02 03:52:23 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:52:23 ERROR: Error reading CSV file C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24600_PUT\nifty_07_oct_24600_put_candles_1min.csv: [Errno 13] Permission denied: 'processed_options_files\\2025-10-01\\NIFTY_07_OCT_24600_PUT_1min_2025-09-22_to_2025-10-01_processed.csv'
2025-10-02 03:52:23 ERROR: Error during options processing: Error reading CSV file: [Errno 13] Permission denied: 'processed_options_files\\2025-10-01\\NIFTY_07_OCT_24600_PUT_1min_2025-09-22_to_2025-10-01_processed.csv'
2025-10-02 03:52:23 INFO: State: SCANNING for trade signals.
2025-10-02 03:52:23 INFO: Flag value before look_for_trade : three_consecutive_same_trades_flag = False
2025-10-02 03:52:23 INFO: Entering function: look_for_trade_signals
2025-10-02 03:52:23 INFO: CALLS Symbol in master dataframes: NIFTY 07 OCT 24700 CALL and symbol.split: CALL
2025-10-02 03:52:23 INFO: call_option_symbol: NIFTY 07 OCT 24700 CALL and put_option_symbol: None
2025-10-02 03:52:23 INFO: Exiting look_for_trade_signals: Could not find both CALL and PUT symbols
2025-10-02 03:52:23 INFO: ============================================================
2025-10-02 03:52:23 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-10-02 03:52:23 INFO: ============================================================

2025-10-02 03:52:23 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
2025-10-02 03:53:38 INFO: === ****************************************************** ===
2025-10-02 03:53:38 INFO: === ****************START LOGGING* (03:53:38) ************ ===
2025-10-02 03:53:38 INFO: === ****************************************************** ===
2025-10-02 03:53:38 INFO: === Algo Trading Bot Started ===
2025-10-02 03:53:38 INFO: Output for processed NIFTY data: processed_files
2025-10-02 03:53:38 INFO: Output for processed Options data: processed_options_files
2025-10-02 03:53:38 INFO: Options input file: User_options_input.csv
2025-10-02 03:53:38 INFO: ============================================================
2025-10-02 03:53:38 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-10-02 03:53:38 INFO: --- Processing Historical NIFTY Data (03:53:38) ---
2025-10-02 03:53:38 INFO:  First run: scheduling all intervals for full historical fetch
2025-10-02 03:53:38 INFO:  Fetching 13 1min data...
2025-10-02 03:53:38 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-10-02 03:53:38 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-10-01\NIFTY\nifty_candles_1min.csv
2025-10-02 03:53:38 INFO: Data fetched from 2025-09-22 09:15:00 to 2025-10-01 15:31:00 for NIFTY for interval 1
2025-10-02 03:53:38 INFO: Data fetched from 2025-09-22 09:15:00 to 2025-10-01 15:31:00
2025-10-02 03:53:38 INFO:  Fetched 3002 new records
2025-10-02 03:53:39 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:31:00
2025-10-02 03:53:39 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-10-02 03:53:39 INFO:  Saved processed file: NIFTY_1min_2025-09-22_to_2025-10-01_processed.csv
2025-10-02 03:53:39 INFO:  Fetching 13 60min data...
2025-10-02 03:53:39 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-10-02 03:53:39 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-10-01\NIFTY\nifty_candles_1hour.csv
2025-10-02 03:53:39 INFO: Data fetched from 2025-08-04 09:15:00 to 2025-10-01 15:15:00 for NIFTY for interval 60
2025-10-02 03:53:39 INFO: Data fetched from 2025-08-04 09:15:00 to 2025-10-01 15:15:00
2025-10-02 03:53:39 INFO:  Fetched 287 new records
2025-10-02 03:53:39 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:15:00
2025-10-02 03:53:39 INFO:  Saved processed file: NIFTY_60min_2025-08-04_to_2025-10-01_processed.csv
2025-10-02 03:53:39 INFO:  Fetching 13 dailymin data...
2025-10-02 03:53:39 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-10-02 03:53:39 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-10-01\NIFTY\nifty_candles_daily.csv
2025-10-02 03:53:39 INFO: Data fetched from 2024-10-03 00:00:00 to 2025-10-01 09:15:00 for NIFTY for interval daily
2025-10-02 03:53:39 INFO: Data fetched from 2024-10-03 00:00:00 to 2025-10-01 09:15:00
2025-10-02 03:53:39 INFO:  Fetched 250 new records
2025-10-02 03:53:39 INFO:   \u2192 No zones affected by latest candle 2025-10-01 09:15:00
2025-10-02 03:53:39 INFO:  Saved processed file: NIFTY_daily_2024-10-03_to_2025-10-01_processed.csv
2025-10-02 03:53:39 INFO:  Fetching 13 5min data...
2025-10-02 03:53:39 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-10-02 03:53:39 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-10-01\NIFTY\nifty_candles_5min.csv
2025-10-02 03:53:39 INFO: Data fetched from 2025-09-11 09:15:00 to 2025-10-01 15:30:00 for NIFTY for interval 5
2025-10-02 03:53:39 INFO: Data fetched from 2025-09-11 09:15:00 to 2025-10-01 15:30:00
2025-10-02 03:53:39 INFO:  Fetched 1126 new records
2025-10-02 03:53:39 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:53:39 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-10-02 03:53:39 INFO:  Saved processed file: NIFTY_5min_2025-09-11_to_2025-10-01_processed.csv
2025-10-02 03:53:39 INFO: Resistant zone: Supply low=24847.45, high=24867.85, status=Tested
2025-10-02 03:53:39 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-10-02 03:53:39 INFO: End date: 2025-10-01
2025-10-02 03:53:39 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24700_CALL
2025-10-02 03:53:39 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24700_CALL\nifty_07_oct_24700_call_candles_1min.csv for interval 1
2025-10-02 03:53:39 INFO: Data fetched from 2025-09-22 09:18:00 to 2025-10-01 15:30:00 for NIFTY 07 OCT 24700 CALL for interval 1
2025-10-02 03:53:39 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:53:39 INFO:      Saved 1min data: NIFTY_07_OCT_24700_CALL_1min_2025-09-22_to_2025-10-01_processed.csv
2025-10-02 03:53:39 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24700_CALL\nifty_07_oct_24700_call_candles_5min.csv for interval 5
2025-10-02 03:53:39 INFO: Data fetched from 2025-09-11 10:05:00 to 2025-10-01 15:30:00 for NIFTY 07 OCT 24700 CALL for interval 5
2025-10-02 03:53:40 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:53:40 INFO:      Saved 5min data: NIFTY_07_OCT_24700_CALL_5min_2025-09-11_to_2025-10-01_processed.csv
2025-10-02 03:53:40 INFO: Options processing completed: 0 successful, 0 failed
2025-10-02 03:53:40 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-10-02 03:53:40 INFO: End date: 2025-10-01
2025-10-02 03:53:40 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24600_PUT
2025-10-02 03:53:40 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24600_PUT\nifty_07_oct_24600_put_candles_1min.csv for interval 1
2025-10-02 03:53:40 INFO: Data fetched from 2025-09-22 09:15:00 to 2025-10-01 15:30:00 for NIFTY 07 OCT 24600 PUT for interval 1
2025-10-02 03:53:40 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:53:40 INFO:      Saved 1min data: NIFTY_07_OCT_24600_PUT_1min_2025-09-22_to_2025-10-01_processed.csv
2025-10-02 03:53:40 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-10-01\NIFTY_07_OCT_24600_PUT\nifty_07_oct_24600_put_candles_5min.csv for interval 5
2025-10-02 03:53:40 INFO: Data fetched from 2025-09-11 09:15:00 to 2025-10-01 15:30:00 for NIFTY 07 OCT 24600 PUT for interval 5
2025-10-02 03:53:40 INFO:   \u2192 No zones affected by latest candle 2025-10-01 15:30:00
2025-10-02 03:53:40 INFO:      Saved 5min data: NIFTY_07_OCT_24600_PUT_5min_2025-09-11_to_2025-10-01_processed.csv
2025-10-02 03:53:40 INFO: Options processing completed: 0 successful, 0 failed
2025-10-02 03:53:40 INFO: State: SCANNING for trade signals.
2025-10-02 03:53:40 INFO: Flag value before look_for_trade : three_consecutive_same_trades_flag = False
2025-10-02 03:53:40 INFO: Entering function: look_for_trade_signals
2025-10-02 03:53:40 INFO: CALLS Symbol in master dataframes: NIFTY 07 OCT 24700 CALL and symbol.split: CALL
2025-10-02 03:53:40 INFO: call_option_symbol: NIFTY 07 OCT 24700 CALL and put_option_symbol: None
2025-10-02 03:53:40 INFO: PUTS Symbol in master dataframes: NIFTY 07 OCT 24600 PUT and symbol.split: PUT
2025-10-02 03:53:40 INFO: call_option_symbol: NIFTY 07 OCT 24700 CALL and put_option_symbol: NIFTY 07 OCT 24600 PUT
