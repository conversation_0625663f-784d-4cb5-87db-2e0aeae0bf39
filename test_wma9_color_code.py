#!/usr/bin/env python3
"""
Test script for the calculate_wma9_color_code function.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the utils directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from processing_functions import calculate_wma9_color_code

def create_test_dataframe():
    """Create a test DataFrame with crossover signals."""

    # Create a sample DataFrame with crossover signals
    data = {
        'timestamp': pd.date_range('2025-01-01', periods=25, freq='1min'),
        'close': [100 + i for i in range(25)],  # Simple increasing price
        'high': [101 + i for i in range(25)],
        'low': [99 + i for i in range(25)],
        'crossover_signal_5_9': [' '] * 25,
        'crossover_signal_9_20': [' '] * 25,
        'crossover_signal_9_26': [' '] * 25,
        'crossover_signal_9_45': [' '] * 25,
        'crossover_signal_9_65': [' '] * 25,
    }

    df = pd.DataFrame(data)

    # Test scenario: yellow -> blue -> cyan -> yellow -> orange -> blue
    # Initial downward crossover at index 2 (should start with yellow)
    df.loc[2, 'crossover_signal_5_9'] = 'downward'

    # Upward crossover at index 5 (yellow -> blue, setting_initial_color_flag=True)
    df.loc[5, 'crossover_signal_5_9'] = 'upward'

    # Upward crossover in 9_20 at index 8 (blue -> cyan, setting_initial_color_flag=False)
    df.loc[8, 'crossover_signal_9_20'] = 'upward'

    # Downward crossover at index 12 (cyan -> yellow)
    df.loc[12, 'crossover_signal_5_9'] = 'downward'

    # Downward crossover in 9_26 at index 15 (yellow -> orange)
    df.loc[15, 'crossover_signal_9_26'] = 'downward'

    # Upward crossover at index 18 (orange -> blue)
    df.loc[18, 'crossover_signal_5_9'] = 'upward'

    return df

def test_calculate_wma9_color_code():
    """Test the calculate_wma9_color_code function."""
    
    print("Testing calculate_wma9_color_code function...")
    
    # Test 1: Normal case with crossover signals
    print("\n=== Test 1: Normal case ===")
    df_test = create_test_dataframe()
    
    print("Input DataFrame:")
    print(df_test[['timestamp', 'crossover_signal_5_9', 'crossover_signal_9_20']].head(10))
    
    # Apply the function
    result_df = calculate_wma9_color_code(df_test)
    
    print("\nResult DataFrame with WMA9_color_code:")
    print(result_df[['timestamp', 'crossover_signal_5_9', 'crossover_signal_9_20', 'WMA9_color_code']].head(10))
    
    # Test 2: Empty DataFrame
    print("\n=== Test 2: Empty DataFrame ===")
    empty_df = pd.DataFrame()
    result_empty = calculate_wma9_color_code(empty_df)
    print(f"Empty DataFrame result: {len(result_empty)} rows")
    
    # Test 3: Missing columns
    print("\n=== Test 3: Missing required columns ===")
    incomplete_df = pd.DataFrame({
        'timestamp': pd.date_range('2025-01-01', periods=5, freq='1min'),
        'close': [100, 101, 102, 103, 104]
    })
    result_incomplete = calculate_wma9_color_code(incomplete_df)
    print(f"Incomplete DataFrame result: {result_incomplete.columns.tolist()}")
    if 'WMA9_color_code' in result_incomplete.columns:
        print(f"WMA9_color_code values: {result_incomplete['WMA9_color_code'].tolist()}")
    
    # Test 4: Verify color transitions
    print("\n=== Test 4: Color transition verification ===")
    df_test = create_test_dataframe()
    result_df = calculate_wma9_color_code(df_test)
    
    # Print the color transitions
    for i, row in result_df.iterrows():
        if row['WMA9_color_code'] != '':
            crossover_info = []
            if row['crossover_signal_5_9'] != ' ':
                crossover_info.append(f"5_9: {row['crossover_signal_5_9']}")
            if row['crossover_signal_9_20'] != ' ':
                crossover_info.append(f"9_20: {row['crossover_signal_9_20']}")
            
            crossover_str = ', '.join(crossover_info) if crossover_info else 'No crossover'
            print(f"Index {i}: Color = {row['WMA9_color_code']}, Crossovers = {crossover_str}")
    
    print("\n=== Test completed successfully! ===")

if __name__ == "__main__":
    test_calculate_wma9_color_code()
