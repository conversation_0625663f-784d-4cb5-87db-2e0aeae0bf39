import os
import datetime
import pandas as pd
import time
import sys
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import config.config as config
import utils.trading_logic as trading_logic
from utils.processing_functions import process_dataframe
from utils.trading_logic import find_latest_signals
from utils.trading_logic import find_latest_other_signals
from utils.tele_bot import send_msg_to_telegram
from utils.processing_functions import get_resistant_zones
from utils.processing_functions import detect_crossovers
from utils.websocket_data_fetch import fetch_index_rows
from utils.websocket_data_fetch import fetch_options_rows
from utils.websocket_data_fetch import check_data_freshness

import warnings
# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')


instrument_list = config.instruments_list
instrument_name = instrument_list[0]['tradingsymbol']

# === Logging Configuration ===
os.makedirs(config.LOG_DIR, exist_ok=True)
log_filename = os.path.join(config.LOG_DIR, f"Daily_log_{datetime.datetime.now().strftime('%Y-%m-%d')}.log")
logging.basicConfig(
    filename=log_filename,
    level=logging.INFO,
    # level=logging.DEBUG,
    format='%(asctime)s %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def look_for_resistant_zones(df_1, df_5, trade_resistant_df):
    resistant_zones = get_resistant_zones(df_5, df_1.iloc[-1]['low'], 'Demand')
    if not resistant_zones.empty:
        all_resistant_demand_zones = resistant_zones[resistant_zones['Zones_sup_dem'] == 'Demand']
        # Prepare DataFrame with the required columns
        if not all_resistant_demand_zones.empty:
            new_rows = all_resistant_demand_zones.rename(
                columns={
                    'Zones_sup_dem': 'Resistant_Zone',
                    'low': 'low',
                    'high': 'high',
                    'Zone_Status': 'Zone_Status'
                }
            )[["Resistant_Zone", "low", "high", "Zone_Status"]]
            trade_resistant_df = pd.concat([trade_resistant_df, new_rows], ignore_index=True)
    
    resistant_zones = get_resistant_zones(df_5, df_1.iloc[-1]['high'], 'Supply')
    if not resistant_zones.empty:
        all_resistant_supply_zones = resistant_zones[resistant_zones['Zones_sup_dem'] == 'Supply']
        # Prepare DataFrame with the required columns
        if not all_resistant_supply_zones.empty:
            new_rows = all_resistant_supply_zones.rename(
                columns={
                    'Zones_sup_dem': 'Resistant_Zone',
                    'low': 'low',
                    'high': 'high',
                    'Zone_Status': 'Zone_Status'
                }
            )[["Resistant_Zone", "low", "high", "Zone_Status"]]
            trade_resistant_df = pd.concat([trade_resistant_df, new_rows], ignore_index=True)

    return trade_resistant_df


def main():
    """
    Main function to run the trading bot.
    """
    logging.info("=== ****************************************************** ===")
    logging.info(f"=== ****************START LOGGING* ({datetime.datetime.now().strftime('%H:%M:%S')}) ************ ===")
    logging.info("=== ****************************************************** ===")

    print("=== Algo Trading Bot Started ===")
    print(f"Output for processed {instrument_name} data: {config.OUTPUT_DIR}")
    print(f"Output for processed Options data: {config.OPTIONS_OUTPUT_DIR}")
    print(f"Options input file: {config.OPTIONS_CSV_FILE}")
    print("="*60)
    
    logging.info("=== Algo Trading Bot Started ===")
    logging.info(f"Output for processed {instrument_name} data: {config.OUTPUT_DIR}")
    logging.info(f"Output for processed Options data: {config.OPTIONS_OUTPUT_DIR}")
    logging.info(f"Options input file: {config.OPTIONS_CSV_FILE}")
    logging.info("="*60)

    # Initialize data stores and state variables
    first_run = True
    last_15_min_fetch_minute = -1
    
    # State management
    # Possible states: 'SCANNING', 'SIGNAL_FOUND', 'TRADE_READY', 'IN_TRADE'
    trade_state = 'SCANNING' 
    trade_info = {} # To store trade details
    master_dataframes = {}
    master_dataframes_call = {}
    master_dataframes_put = {}


    trade_log_columns = [
        "Instrument_name",
        "trade_entry_time",
        "trade_exit_time",
        "Trade_entry_price",
        "Trade_exit_price",
        "profit_loss"
    ]
    trade_log_df = pd.DataFrame(columns=trade_log_columns)
    three_consecutive_same_trades_flag = False
    repeated_option_type = None

    trade_resistant_columns = [
        "Resistant_Zone",
        "low",
        "high",
        "Zone_Status"
    ]
    trade_resistant_df = pd.DataFrame(columns=trade_resistant_columns)

    Zones_affected_1 = None
    Zones_affected_5 = None
    Zones_affected_60 = None

    # Create output directories if they don't exist
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    os.makedirs(config.OPTIONS_OUTPUT_DIR, exist_ok=True)
    os.makedirs(config.OUTPUT_DIR_TRADE_LOG, exist_ok=True)

    df_1, df_5, df_60 = None, None, None
    to_date_str = datetime.datetime.now().strftime('%Y-%m-%d')

    #output_path_1min = None

    #instrument_list = config.instruments_list

    # Message to be sent for telegram bot   
    message_to_send = f"Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> \n Example only ===Algo_Trading===$TRADE$BUY${instrument_name} 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30"
    send_msg_to_telegram(message_to_send)

    #Check if tradelog file is already there
    os.makedirs(config.OUTPUT_DIR_TRADE_LOG, exist_ok=True)
    tradelog_filename = f"trade_log_{to_date_str}.csv"
    tradelog_path = os.path.join(config.OUTPUT_DIR_TRADE_LOG, tradelog_filename)
    if os.path.exists(tradelog_path):
        try:
            existing_df = pd.read_csv(tradelog_path)
            if existing_df is not None and not existing_df.empty:
                logging.info(f"Trade log found. Last trade entry time: {existing_df['trade_entry_time'].iloc[-1]} and symbol: {existing_df['Instrument_name'].iloc[-1]}")
                trade_info['last_trade_crossover_timestamp'] = pd.to_datetime(existing_df['trade_entry_time'].iloc[-1]) # also can use existing_df.iloc[0, 1]
                trade_info['prev_trade_symbol'] = existing_df['Instrument_name'].iloc[-1]  # also can use existing_df.iloc[0, 0]
        except Exception as e:
            logging.error(f"Empty Tradelog file or error reading file: {e}")
    
    while True:
        now = datetime.datetime.now()
        current_time = now.time()
        
        #current_time = now.time()
        # 
        # --- Historical {instrument['tradingsymbol']} Data Processing ---
        print(f"\n--- Processing Historical {instrument_name} Data ({now.strftime('%H:%M:%S')}) ---")
        logging.info(f"--- Processing Historical {instrument_name} Data ({now.strftime('%H:%M:%S')}) ---")

        intervals_to_fetch = set()
        if first_run or current_time < config.START_TRADE or current_time > config.STOP_TRADE:
            print("→ First run: scheduling all intervals for full historical fetch")
            logging.info(" First run: scheduling all intervals for full historical fetch")
            #intervals_to_fetch.update(["1", "5", "60"])
            intervals_to_fetch.update(["1", "5", "60", "daily"])
            first_run = False
        else:
            intervals_to_fetch.update(["1", "5"])

        if now.minute % 15 == 0 and now.minute != last_15_min_fetch_minute:
            print(f"→ Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
            logging.info(f" Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
            intervals_to_fetch.add("60")
            last_15_min_fetch_minute = now.minute
        
        for instrument in instrument_list: 
            #results = []
            for interval in intervals_to_fetch:
                print(f"  → Fetching {instrument['security_id']} {interval}min data...")
                logging.info(f" Fetching {instrument['security_id']} {interval}min data...")
                df, from_date_str, to_date_str = fetch_index_rows(interval, instrument["tradingsymbol"])

                if df is not None and not df.empty:
                    print(f"    ✓ Fetched {len(df)} new records")
                    logging.info(f" Fetched {len(df)} new records")
                    
                    df_processed, Zones_affected = process_dataframe(df)
                    
                    if interval == "1": 
                        # Always Check if fresh data is being processed
                        # now = datetime.datetime.now()
                        # now_timestamp = pd.Timestamp(now)
                        # current_time = now.time()
                        # logging.info(f"Checking if fresh index data is being processed. ")
                        # if not check_data_freshness(df_processed):
                        #     logging.error("Fresh data not coming in for Index. Please check Websocket connection")
                        #     sys.exit(1)
                        # else:
                            df_1 = df_processed
                            Zones_affected_1 = Zones_affected
                            #display content of Zones_affected
                            if Zones_affected_1 is not None and not Zones_affected_1.empty:
                                #loop through each row of Zones_affected_1 and print it
                                logging.info("Zones Affected for 1 minute")
                                for idx, row in Zones_affected_1.iterrows():
                                    logging.info(f"Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")
                            else:
                                logging.info("No 1 minute zones affected. Zones_affected_1 is empty")
                    elif interval == "5": 
                        df_5 = df_processed
                        Zones_affected_5 = Zones_affected
                        #display content of Zones_affected
                        if Zones_affected_5 is not None and not Zones_affected_5.empty:
                            #loop through each row of Zones_affected_5 and print it
                            logging.info("Zones Affected for 5 minute")
                            for idx, row in Zones_affected_5.iterrows():
                                logging.info(f"Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")
                        else:
                            logging.info("No 5 minute zones affected. Zones_affected_5 is empty")
                    elif interval == "60": 
                        df_60 = df_processed
                        Zones_affected_60 = Zones_affected
                    
                    # Save processed file
                    try:
                        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
                        # create directory withrespect to instrument name
                        instrument_dir = os.path.join(config.OUTPUT_DIR, instrument['tradingsymbol'])
                        os.makedirs(instrument_dir, exist_ok=True)
                        # create a day folder with name as current day date then store the .csv inside it. Also check if the folder already exists
                        day_dir = os.path.join(instrument_dir, to_date_str)
                        os.makedirs(day_dir, exist_ok=True)
                        # Save CSV inside the day folder
                        interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                        output_filename = f"{instrument['tradingsymbol']}_{interval_suffix}_{from_date_str}_to_{to_date_str}_processed.csv"
                        output_path = os.path.join(day_dir, output_filename)
                        # if interval == "1":
                        #     output_path_1min = output_path
                        df_processed.to_csv(output_path, index=False)
                        print(f"    ✓ Saved processed file: {output_filename}")
                        logging.info(f" Saved processed file: {output_filename}")
                    except Exception as e:
                        print(f"    ✗ Error saving processed data for interval {interval}: {str(e)}")
                        logging.error(f" Error saving processed data for interval {interval}: {str(e)}")
                else:
                    print(f"    ✗ No data available for {instrument['tradingsymbol']} {interval}min interval")
                    logging.error(f" No data available for {instrument['tradingsymbol']} {interval}min interval")

        # --- Trading Logic State Machine ---

        #Find crossovers between 1min and 60min for brother
        # if df_1 is not None and df_60 is not None:
        #     df_1 = detect_crossovers_diff_timeframes(df_1, df_60, 'WMA9', 'WMA9')
        #     df_1.to_csv(output_path_1min, index=False)
        # if df_1 is not None:
        #     df_1 = detect_crossovers(df_1, 'WMA45', 'WMA225')
        #     df_1 = detect_crossovers(df_1, 'WMA225', 'WMA325')
        #     df_1.to_csv(output_path_1min, index=False)

        now = datetime.datetime.now()
        current_time = now.time()


        # Example usage of get_resistant_zones function
        
        # Get current price from latest candle
        # current_price = 25565.15
        # option_type = "CALL"
        
        # # Find resistant zones at current price
        # resistant_zones = get_resistant_zones(df_1, current_price, 'Both')
        
        # # Use resistant zones for decision making
        # if not resistant_zones.empty:
        #     logging.info(f"Found {len(resistant_zones)} resistant zones at price {current_price}")
            
        #     # Example: Adjust stop loss based on nearest resistant zone
        #     if option_type == "CALL" and 'Supply' in resistant_zones['Zones_sup_dem'].values:
        #         nearest_supply = resistant_zones[resistant_zones['Zones_sup_dem'] == 'Supply'].iloc[0]
        #         logging.info(f"Found supply zone at {nearest_supply['low']} - {nearest_supply['high']}")
        #         logging.info(f"zone timestamp: {nearest_supply['timestamp']}")
        #         # Adjust stop loss or take other actions
                
        #     elif option_type == "PUT" and 'Demand' in resistant_zones['Zones_sup_dem'].values:
        #         nearest_demand = resistant_zones[resistant_zones['Zones_sup_dem'] == 'Demand'].iloc[0]
        #         logging.info(f"Found demand zone at {nearest_demand['low']} - {nearest_demand['high']}")
        #         logging.info(f"zone timestamp: {nearest_demand['timestamp']}")
        #         # Adjust stop loss or take other actions
        
        # # Continue with existing code...

        trade_resistant_df = trade_resistant_df.iloc[0:0]
        trade_resistant_df = look_for_resistant_zones(df_1, df_5, trade_resistant_df)
        # printing all rows of trade_resistant_df
        if not trade_resistant_df.empty:
            for idx, row in trade_resistant_df.iterrows():
                logging.info(f"Resistant zone: {row['Resistant_Zone']} low={row['low']}, high={row['high']}, status={row['Zone_Status']}")
                print(f"Resistant zone: {row['Resistant_Zone']} low={row['low']}, high={row['high']}, status={row['Zone_Status']}")

        # ##### START of fetch OPTION DATA into CSV whenever need to feed into Testing program ############
        if trade_state == 'SCANNING':
            print("\n--- Processing Option Data for CALL---")
            try:
                options_results, lot_size = fetch_options_rows("CALL", instrument['tradingsymbol'])
                if options_results["success"]:
                    master_dataframes_call = options_results.get('dataframes', {})
                    logging.info(f"Options processing completed: {options_results['successful_fetches']} successful, {options_results['failed_fetches']} failed")
                else:
                    logging.error(f"Options processing failed: {options_results.get('error', 'Unknown error')}")
                    trade_state = 'SCANNING' # Reset on failure
            except Exception as e:
                logging.error(f"Error during options processing: {str(e)}")
                trade_state = 'SCANNING' # Reset on failure

            print("\n--- Processing Option Data for PUT---")
            try:
                options_results, lot_size = fetch_options_rows("PUT", instrument['tradingsymbol'])
                if options_results["success"]:
                    master_dataframes_put = options_results.get('dataframes', {})
                    logging.info(f"Options processing completed: {options_results['successful_fetches']} successful, {options_results['failed_fetches']} failed")
                else:
                    logging.error(f"Options processing failed: {options_results.get('error', 'Unknown error')}")
                    trade_state = 'SCANNING' # Reset on failure
            except Exception as e:
                logging.error(f"Error during options processing: {str(e)}")
                trade_state = 'SCANNING' # Reset on failure

        # ##### END of fetch OPTION DATA into CSV whenever need to feed into Testing program ############

        if trade_state == 'SCANNING':# and current_time >= config.START_TRADE and current_time <= config.STOP_TRADE:
            logging.info("State: SCANNING for trade signals.")
            logging.info(f"Flag value before look_for_trade : three_consecutive_same_trades_flag = {three_consecutive_same_trades_flag}")
            option_type, signal_found, trade_info = trading_logic.look_for_trade_signals(df_1, df_5, trade_info, master_dataframes_call, master_dataframes_put, instrument['tradingsymbol'], Zones_affected_1, Zones_affected_5)
            if signal_found == 'True':
                latest_other_signals_1min = find_latest_other_signals(df_1)
                logging.info(f"latest_crossover_9_90 {latest_other_signals_1min['latest_crossover_9_90']['crossover_signal_9_90']} timestamp: {latest_other_signals_1min['latest_crossover_9_90']['timestamp']}")
                if (three_consecutive_same_trades_flag == True and 
                    repeated_option_type == option_type and                    
                    latest_other_signals_1min['latest_crossover_9_90']['timestamp'] < first_consecutive_trade_timestamp):
                        trade_state = 'SCANNING'
                        logging.info(f"Flagged: Last three trades are all '{repeated_option_type}'. Skipping signal for same type.")
                else:
                    trade_info["option_type"] = option_type
                    trade_state = 'SIGNAL_FOUND'
                    logging.info(f"State change: SCANNING -> SIGNAL_FOUND. Signal for {option_type}.")
            
        if trade_state in ['SIGNAL_FOUND', 'TRADE_READY', 'IN_TRADE']:
            print("\n--- Processing Option Data ---")
            logging.info("--- Processing Option Data ---")
            try:
                options_results, lot_size = fetch_options_rows(trade_info["option_type"], instrument['tradingsymbol'])
                if options_results["success"]:
                    master_dataframes = options_results.get('dataframes', {})
                    logging.info(f"Options processing completed: {options_results['successful_fetches']} successful, {options_results['failed_fetches']} failed")
                else:
                    logging.error(f"Options processing failed: {options_results.get('error', 'Unknown error')}")
                    trade_state = 'SCANNING' # Reset on failure
            except Exception as e:
                logging.error(f"Error during options processing: {str(e)}")
                trade_state = 'SCANNING' # Reset on failure

        if trade_state == 'SIGNAL_FOUND':
            logging.info("State: SIGNAL_FOUND. Getting trade info.")
            trade_symbol, entry_candle = trading_logic.get_info_for_trade(master_dataframes, trade_info["option_type"])
            if trade_symbol is not None and entry_candle is not None:
                trade_info['symbol'] = trade_symbol
                trade_info['entry_candle'] = entry_candle
                trade_info['lot_size'] = lot_size
                trade_state = 'TRADE_READY'
                logging.info(f"State change: SIGNAL_FOUND -> TRADE_READY for {trade_symbol}.")
            else:
                logging.warning(f"Could not find a tradable option for {trade_info['option_type']}. Resetting.")
                trade_state = 'SCANNING'

        if trade_state == 'TRADE_READY':
            logging.info(f"State: TRADE_READY. Attempting to take trade for {trade_info['symbol']}.")
            time_returned, trade_status = trading_logic.take_the_trade(
                trade_info['symbol'], trade_info['entry_candle'], master_dataframes, Zones_affected_1, Zones_affected_5, df_1
            )
            if trade_status == "Trade Taken":
                trade_info['entry_time'] = time_returned
                trade_info['entry_price'] = master_dataframes[trade_info['symbol']]['df_opt_1min'].iloc[-1]['close']

                Option_1min_crossover_and_zone = find_latest_signals(master_dataframes[trade_info['symbol']]['df_opt_1min'])
                trade_info['opt_1min_latest_Demand'] = Option_1min_crossover_and_zone['latest_zone']
                trade_info['opt_1min_latest_Supply'] = Option_1min_crossover_and_zone['prior_to_latest_zone']
                trade_info['stop_loss'] = Option_1min_crossover_and_zone['latest_zone']['low']
                Option_5min_crossover_and_zone = find_latest_signals(master_dataframes[trade_info['symbol']]['df_opt_5min'])
                trade_info['opt_5min_latest_Demand'] = Option_5min_crossover_and_zone['latest_zone']
                trade_info['opt_5min_latest_Supply'] = Option_5min_crossover_and_zone['prior_to_latest_zone']
                trade_info['in_profit'] = "False"
                
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$TRADE$BUY${trade_info['symbol']}${trade_info['entry_price']}${trade_info['stop_loss']}${trade_info['entry_time']}"
                send_msg_to_telegram(message_to_send)

                new_row = {"Instrument_name": trade_info['symbol'], "trade_entry_time": trade_info['entry_time'], "Trade_entry_price": trade_info['entry_price']}
                trade_log_df = pd.concat([trade_log_df, pd.DataFrame([new_row])], ignore_index=True)
                
                trade_state = 'IN_TRADE'
                logging.info(f"State change: TRADE_READY -> IN_TRADE. Trade taken for {trade_info['symbol']} at {trade_info['entry_price']}.")
                logging.info(f"Latest option 1 min demand : {trade_info['opt_1min_latest_Demand']['timestamp']}")
                logging.info(f"Latest option 1 min supply : {trade_info['opt_1min_latest_Supply']['timestamp']}")
                logging.info(f"Stop loss is initially set to : {trade_info['stop_loss']}")
            elif trade_status == "False":
                logging.info(f"Trade invalidated for {trade_info['symbol']}. Resetting.")
                trade_state = 'SCANNING'
                prev_trade_entry_crossover_timestamp = time_returned
                prev_trade_symbol = trade_info['symbol']
                trade_info = {} # Reset trade info
                trade_info['last_trade_crossover_timestamp'] = prev_trade_entry_crossover_timestamp
                trade_info['prev_trade_symbol'] = prev_trade_symbol
                logging.info(f"trade_info value populated when take trade is invalidated: {prev_trade_symbol} and {prev_trade_entry_crossover_timestamp}")
                

        if trade_state == 'IN_TRADE':
            logging.info(f"State: IN_TRADE. Monitoring trade for {trade_info['symbol']}.")
            # exit_status, trade_info['stop_loss'], trade_info['exit_reason'] = trading_logic.check_for_trade_exit(
            #     df_1, df_5, trade_info['symbol'], trade_info['entry_time'], master_dataframes, trade_log_df, trade_resistant_df, trade_info['opt_1min_latest_Demand'],
            #     trade_info['opt_1min_latest_Supply'], trade_info['opt_5min_latest_Demand'], trade_info['opt_5min_latest_Supply'], trade_info['stop_loss'], trade_info['entry_reason'],
            #     Zones_affected_1, Zones_affected_5
            # )
            exit_status, trade_info = trading_logic.check_for_trade_exit(
                df_1, df_5, trade_info, trade_log_df, trade_resistant_df, Zones_affected_1, Zones_affected_5, master_dataframes
            )
            if exit_status == 'Trade Exit' or current_time == config.MARKET_CLOSE: # - datetime.timedelta(minutes=5)):
                if exit_status == 'Trade Exit':
                    logging.info(f"Exit signal received for {trade_info['symbol']}. Exiting trade.")
                    logging.info(f"Exit reason: {trade_info['exit_reason']}")
                else:
                    logging.info(f"Market close reached. Exiting trade {trade_info['symbol']}")
                    trade_info['exit_reason'] = 'Market_Close'
                    logging.info(f"Exit reason: {trade_info['exit_reason']}")
                
                trading_logic.exit_the_trade(
                    trade_info, master_dataframes, trade_log_df, to_date_str
                )
                trade_state = 'SCANNING'
                prev_trade_entry_crossover_timestamp = trade_info['last_trade_crossover_timestamp']
                prev_trade_symbol = trade_info['symbol']
                trade_info = {} # Reset trade info
                trade_info['last_trade_crossover_timestamp'] = prev_trade_entry_crossover_timestamp
                trade_info['prev_trade_symbol'] = prev_trade_symbol
                logging.info(f"trade_info value populated after one trade is exited: {prev_trade_symbol} and {prev_trade_entry_crossover_timestamp}")
                #check for consecutive last three trades of same kind of trades and flag
                if len(trade_log_df) >= 3:
                    # Get the last three entries
                    last_three = trade_log_df.tail(3)
                    # Extract the last word from each instrument_name
                    last_words = last_three["Instrument_name"].astype(str).str.split().str[-1]
                    # Check if all last words are the same
                    if last_words.nunique() == 1:
                        last_three_trade_entry_timestamp = last_three["trade_entry_time"]
                        first_consecutive_trade_timestamp = last_three_trade_entry_timestamp.iloc[0]
                        repeated_option_type = last_words.iloc[0]
                        three_consecutive_same_trades_flag = True
                        print(f"Flagged: Last three trades are all '{repeated_option_type}' and first one happened at: {first_consecutive_trade_timestamp}")
                    else:
                        three_consecutive_same_trades_flag = False
                        repeated_option_type = None
                else:
                    three_consecutive_same_trades_flag = False
                    repeated_option_type = None

                logging.info(f"Flag value after exiting trade : three_consecutive_same_trades_flag = {three_consecutive_same_trades_flag}")
                logging.info("State change: IN_TRADE -> SCANNING. Trade exited.")

        print(f"\n{'='*60}")
        print(f"Current State: {trade_state}. Next update in {config.FETCH_INTERVAL_SECONDS} seconds...")
        print(f"{'='*60}\n")
        logging.info(f"{'='*60}")
        logging.info(f"Current State: {trade_state}. Next update in {config.FETCH_INTERVAL_SECONDS} seconds...")
        logging.info(f"{'='*60}\n")

        if current_time > config.MARKET_CLOSE:
            print(f"→ Market is closed (current time: {current_time}). Exiting...")
            logging.info(f"Market is closed (current time: {current_time}). Exiting...")
            break
        elif current_time < config.MARKET_START:
            message_to_send = f"===Algo_Trading===$INTIMATION$Premarket time now for {instrument_name}. Sleeping for 10 min."
            send_msg_to_telegram(message_to_send)
            time.sleep(config.PRE_MARKET_INTERVAL_SECONDS)
        elif current_time == config.MARKET_CLOSE:    
            time.sleep(config.FETCH_INTERVAL_SECONDS)
        else:
            time.sleep(config.FETCH_INTERVAL_SECONDS)

if __name__ == "__main__":
    main()
