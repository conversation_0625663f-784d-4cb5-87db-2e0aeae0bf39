2025-09-27 03:13:48 INFO: === ****************************************************** ===
2025-09-27 03:13:48 INFO: === ****************START LOGGING* (03:13:48) ************ ===
2025-09-27 03:13:48 INFO: === ****************************************************** ===
2025-09-27 03:13:48 INFO: === Algo Trading Bot Started ===
2025-09-27 03:13:48 INFO: Output for processed NIFTY data: processed_files
2025-09-27 03:13:48 INFO: Output for processed Options data: processed_options_files
2025-09-27 03:13:48 INFO: Options input file: User_options_input.csv
2025-09-27 03:13:48 INFO: ============================================================
2025-09-27 03:13:48 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-27 03:13:48 INFO: --- Processing Historical NIFTY Data (03:13:48) ---
2025-09-27 03:13:48 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-27 03:13:48 INFO:  Fetching 13 dailymin data...
2025-09-27 03:13:48 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-27 03:13:48 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_daily.csv
2025-09-27 03:13:48 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00 for NIFTY for interval daily
2025-09-27 03:13:48 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00
2025-09-27 03:13:48 INFO:  Fetched 250 new records
2025-09-27 03:13:49 INFO:   \u2192 No zones affected by latest candle 2025-09-26 09:15:00
2025-09-27 03:13:49 INFO:  Saved processed file: NIFTY_daily_2024-09-27_to_2025-09-26_processed.csv
2025-09-27 03:13:49 INFO:  Fetching 13 60min data...
2025-09-27 03:13:49 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-27 03:13:49 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1hour.csv
2025-09-27 03:13:49 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00 for NIFTY for interval 60
2025-09-27 03:13:49 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00
2025-09-27 03:13:49 INFO:  Fetched 301 new records
2025-09-27 03:13:49 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:15:00
2025-09-27 03:13:49 INFO:  Saved processed file: NIFTY_60min_2025-07-28_to_2025-09-26_processed.csv
2025-09-27 03:13:49 INFO:  Fetching 13 5min data...
2025-09-27 03:13:49 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-27 03:13:49 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_5min.csv
2025-09-27 03:13:49 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00 for NIFTY for interval 5
2025-09-27 03:13:49 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00
2025-09-27 03:13:49 INFO:  Fetched 1133 new records
2025-09-27 03:13:49 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:05:00
2025-09-27 03:13:49 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-27 03:13:49 INFO:  Saved processed file: NIFTY_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:13:49 INFO:  Fetching 13 1min data...
2025-09-27 03:13:49 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-27 03:13:49 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1min.csv
2025-09-27 03:13:49 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00 for NIFTY for interval 1
2025-09-27 03:13:49 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00
2025-09-27 03:13:49 INFO:  Fetched 3414 new records
2025-09-27 03:13:51 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:08:00
2025-09-27 03:13:51 INFO: Checking if fresh index data is being processed. 
2025-09-27 03:13:51 INFO: Data freshness check:
Current time: 2025-09-27 03:13:51.626029
Latest data time: 2025-09-26 16:08:00
Threshold time: 2025-09-26 16:09:30
2025-09-27 03:13:51 ERROR: Data is stale - Last update was more than 1.5 minutes ago
2025-09-27 03:13:51 ERROR: Fresh data not coming in for Index. Please check Websocket connection
2025-09-27 03:17:54 INFO: === ****************************************************** ===
2025-09-27 03:17:54 INFO: === ****************START LOGGING* (03:17:54) ************ ===
2025-09-27 03:17:54 INFO: === ****************************************************** ===
2025-09-27 03:17:54 INFO: === Algo Trading Bot Started ===
2025-09-27 03:17:54 INFO: Output for processed NIFTY data: processed_files
2025-09-27 03:17:54 INFO: Output for processed Options data: processed_options_files
2025-09-27 03:17:54 INFO: Options input file: User_options_input.csv
2025-09-27 03:17:54 INFO: ============================================================
2025-09-27 03:17:54 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-27 03:17:54 INFO: --- Processing Historical NIFTY Data (03:17:54) ---
2025-09-27 03:17:54 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-27 03:17:54 INFO:  Fetching 13 5min data...
2025-09-27 03:17:54 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-27 03:17:54 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_5min.csv
2025-09-27 03:17:54 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00 for NIFTY for interval 5
2025-09-27 03:17:54 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00
2025-09-27 03:17:54 INFO:  Fetched 1133 new records
2025-09-27 03:17:55 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:05:00
2025-09-27 03:17:55 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-27 03:17:55 INFO:  Saved processed file: NIFTY_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:17:55 INFO:  Fetching 13 60min data...
2025-09-27 03:17:55 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-27 03:17:55 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1hour.csv
2025-09-27 03:17:55 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00 for NIFTY for interval 60
2025-09-27 03:17:55 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00
2025-09-27 03:17:55 INFO:  Fetched 301 new records
2025-09-27 03:17:55 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:15:00
2025-09-27 03:17:55 INFO:  Saved processed file: NIFTY_60min_2025-07-28_to_2025-09-26_processed.csv
2025-09-27 03:17:55 INFO:  Fetching 13 1min data...
2025-09-27 03:17:55 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-27 03:17:55 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1min.csv
2025-09-27 03:17:55 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00 for NIFTY for interval 1
2025-09-27 03:17:55 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00
2025-09-27 03:17:55 INFO:  Fetched 3414 new records
2025-09-27 03:17:57 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:08:00
2025-09-27 03:17:57 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-27 03:17:57 INFO:  Saved processed file: NIFTY_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:17:57 INFO:  Fetching 13 dailymin data...
2025-09-27 03:17:57 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-27 03:17:57 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_daily.csv
2025-09-27 03:17:57 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00 for NIFTY for interval daily
2025-09-27 03:17:57 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00
2025-09-27 03:17:57 INFO:  Fetched 250 new records
2025-09-27 03:17:57 INFO:   \u2192 No zones affected by latest candle 2025-09-26 09:15:00
2025-09-27 03:17:57 INFO:  Saved processed file: NIFTY_daily_2024-09-27_to_2025-09-26_processed.csv
2025-09-27 03:17:57 INFO: Resistant zone: Demand low=24630.35, high=24674.65, status=Tested
2025-09-27 03:17:57 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-09-27 03:17:57 INFO: End date: 2025-09-26
2025-09-27 03:17:57 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL
2025-09-27 03:17:57 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_1min.csv for interval 1
2025-09-27 03:17:57 INFO: Data fetched from 2025-09-16 09:18:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 1
2025-09-27 03:17:58 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:17:58 INFO:      Saved 1min data: NIFTY_30_SEP_24850_CALL_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:17:58 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_5min.csv for interval 5
2025-09-27 03:17:58 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 5
2025-09-27 03:17:58 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:17:58 INFO:      Saved 5min data: NIFTY_30_SEP_24850_CALL_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:17:58 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:17:58 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-09-27 03:17:58 INFO: End date: 2025-09-26
2025-09-27 03:17:58 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT
2025-09-27 03:17:58 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_1min.csv for interval 1
2025-09-27 03:17:58 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 15:31:00 for NIFTY 30 SEP 24900 PUT for interval 1
2025-09-27 03:18:00 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:31:00
2025-09-27 03:18:00 INFO:      Saved 1min data: NIFTY_30_SEP_24900_PUT_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:18:00 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_5min.csv for interval 5
2025-09-27 03:18:00 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24900 PUT for interval 5
2025-09-27 03:18:01 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:18:01 INFO:      Saved 5min data: NIFTY_30_SEP_24900_PUT_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:18:01 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:18:01 INFO: State: SCANNING for trade signals.
2025-09-27 03:18:01 INFO: Flag value before look_for_trade : three_consecutive_same_trades_flag = False
2025-09-27 03:18:01 INFO: Entering function: look_for_trade_signals
2025-09-27 03:18:01 INFO: CALLS Symbol in master dataframes: NIFTY 30 SEP 24850 CALL and symbol.split: CALL
2025-09-27 03:18:01 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: None
2025-09-27 03:18:01 INFO: PUTS Symbol in master dataframes: NIFTY 30 SEP 24900 PUT and symbol.split: PUT
2025-09-27 03:18:01 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: NIFTY 30 SEP 24900 PUT
2025-09-27 03:18:01 INFO: ------------- FOR CALL ------------------
2025-09-27 03:18:01 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:25:00 
2025-09-27 03:18:01 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:18:01 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:18:01 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 14:50:00 
2025-09-27 03:18:01 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 13:15:00 
2025-09-27 03:18:01 INFO: ------------- FOR PUT ------------------
2025-09-27 03:18:01 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:18:00 
2025-09-27 03:18:01 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:18:01 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:18:01 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 15:15:00 
2025-09-27 03:18:01 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:30:00 
2025-09-27 03:18:01 INFO: First trade. No previous crossover timestamp available.
2025-09-27 03:18:01 INFO: new_crossover happened: True
2025-09-27 03:18:01 INFO: Exiting function: look_for_trade_signals (no signal found for NIFTY)
2025-09-27 03:18:01 INFO: ============================================================
2025-09-27 03:18:01 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-27 03:18:01 INFO: ============================================================

2025-09-27 03:18:01 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
2025-09-27 03:28:01 INFO: --- Processing Historical NIFTY Data (03:28:01) ---
2025-09-27 03:28:01 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-27 03:28:01 INFO:  Fetching 13 5min data...
2025-09-27 03:28:01 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-27 03:28:01 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_5min.csv
2025-09-27 03:28:01 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00 for NIFTY for interval 5
2025-09-27 03:28:01 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00
2025-09-27 03:28:01 INFO:  Fetched 1133 new records
2025-09-27 03:28:01 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:05:00
2025-09-27 03:28:01 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-27 03:28:01 INFO:  Saved processed file: NIFTY_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:28:01 INFO:  Fetching 13 60min data...
2025-09-27 03:28:01 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-27 03:28:01 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1hour.csv
2025-09-27 03:28:01 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00 for NIFTY for interval 60
2025-09-27 03:28:01 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00
2025-09-27 03:28:01 INFO:  Fetched 301 new records
2025-09-27 03:28:01 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:15:00
2025-09-27 03:28:01 INFO:  Saved processed file: NIFTY_60min_2025-07-28_to_2025-09-26_processed.csv
2025-09-27 03:28:01 INFO:  Fetching 13 1min data...
2025-09-27 03:28:01 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-27 03:28:01 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1min.csv
2025-09-27 03:28:01 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00 for NIFTY for interval 1
2025-09-27 03:28:01 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00
2025-09-27 03:28:01 INFO:  Fetched 3414 new records
2025-09-27 03:28:03 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:08:00
2025-09-27 03:28:03 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-27 03:28:03 INFO:  Saved processed file: NIFTY_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:28:03 INFO:  Fetching 13 dailymin data...
2025-09-27 03:28:03 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-27 03:28:03 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_daily.csv
2025-09-27 03:28:03 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00 for NIFTY for interval daily
2025-09-27 03:28:03 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00
2025-09-27 03:28:03 INFO:  Fetched 250 new records
2025-09-27 03:28:03 INFO:   \u2192 No zones affected by latest candle 2025-09-26 09:15:00
2025-09-27 03:28:03 INFO:  Saved processed file: NIFTY_daily_2024-09-27_to_2025-09-26_processed.csv
2025-09-27 03:28:03 INFO: Resistant zone: Demand low=24630.35, high=24674.65, status=Tested
2025-09-27 03:28:03 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-09-27 03:28:03 INFO: End date: 2025-09-26
2025-09-27 03:28:03 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL
2025-09-27 03:28:03 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_1min.csv for interval 1
2025-09-27 03:28:03 INFO: Data fetched from 2025-09-16 09:18:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 1
2025-09-27 03:28:04 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:28:04 ERROR: Error reading CSV file C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_1min.csv: [Errno 13] Permission denied: 'processed_options_files\\2025-09-26\\NIFTY_30_SEP_24850_CALL_1min_2025-09-16_to_2025-09-26_processed.csv'
2025-09-27 03:28:04 ERROR: Error during options processing: Error reading CSV file: [Errno 13] Permission denied: 'processed_options_files\\2025-09-26\\NIFTY_30_SEP_24850_CALL_1min_2025-09-16_to_2025-09-26_processed.csv'
2025-09-27 03:28:04 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-09-27 03:28:04 INFO: End date: 2025-09-26
2025-09-27 03:28:04 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT
2025-09-27 03:28:04 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_1min.csv for interval 1
2025-09-27 03:28:04 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 15:31:00 for NIFTY 30 SEP 24900 PUT for interval 1
2025-09-27 03:28:06 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:31:00
2025-09-27 03:28:06 INFO:      Saved 1min data: NIFTY_30_SEP_24900_PUT_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:28:06 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_5min.csv for interval 5
2025-09-27 03:28:06 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24900 PUT for interval 5
2025-09-27 03:28:06 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:28:06 INFO:      Saved 5min data: NIFTY_30_SEP_24900_PUT_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:28:06 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:28:06 INFO: State: SCANNING for trade signals.
2025-09-27 03:28:06 INFO: Flag value before look_for_trade : three_consecutive_same_trades_flag = False
2025-09-27 03:28:06 INFO: Entering function: look_for_trade_signals
2025-09-27 03:28:06 INFO: CALLS Symbol in master dataframes: NIFTY 30 SEP 24850 CALL and symbol.split: CALL
2025-09-27 03:28:06 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: None
2025-09-27 03:28:06 INFO: PUTS Symbol in master dataframes: NIFTY 30 SEP 24900 PUT and symbol.split: PUT
2025-09-27 03:28:06 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: NIFTY 30 SEP 24900 PUT
2025-09-27 03:28:06 INFO: ------------- FOR CALL ------------------
2025-09-27 03:28:06 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:25:00 
2025-09-27 03:28:06 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:28:06 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:28:06 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 14:50:00 
2025-09-27 03:28:06 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 13:15:00 
2025-09-27 03:28:06 INFO: ------------- FOR PUT ------------------
2025-09-27 03:28:06 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:18:00 
2025-09-27 03:28:06 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:28:06 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:28:06 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 15:15:00 
2025-09-27 03:28:06 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:30:00 
2025-09-27 03:28:06 INFO: First trade. No previous crossover timestamp available.
2025-09-27 03:28:06 INFO: new_crossover happened: True
2025-09-27 03:28:06 INFO: Exiting function: look_for_trade_signals (no signal found for NIFTY)
2025-09-27 03:28:06 INFO: ============================================================
2025-09-27 03:28:06 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-27 03:28:06 INFO: ============================================================

2025-09-27 03:28:06 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
2025-09-27 03:38:06 INFO: --- Processing Historical NIFTY Data (03:38:06) ---
2025-09-27 03:38:06 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-27 03:38:06 INFO:  Fetching 13 5min data...
2025-09-27 03:38:06 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-27 03:38:06 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_5min.csv
2025-09-27 03:38:06 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00 for NIFTY for interval 5
2025-09-27 03:38:06 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00
2025-09-27 03:38:06 INFO:  Fetched 1133 new records
2025-09-27 03:38:07 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:05:00
2025-09-27 03:38:07 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-27 03:38:07 INFO:  Saved processed file: NIFTY_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:38:07 INFO:  Fetching 13 60min data...
2025-09-27 03:38:07 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-27 03:38:07 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1hour.csv
2025-09-27 03:38:07 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00 for NIFTY for interval 60
2025-09-27 03:38:07 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00
2025-09-27 03:38:07 INFO:  Fetched 301 new records
2025-09-27 03:38:07 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:15:00
2025-09-27 03:38:07 INFO:  Saved processed file: NIFTY_60min_2025-07-28_to_2025-09-26_processed.csv
2025-09-27 03:38:07 INFO:  Fetching 13 1min data...
2025-09-27 03:38:07 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-27 03:38:07 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1min.csv
2025-09-27 03:38:07 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00 for NIFTY for interval 1
2025-09-27 03:38:07 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00
2025-09-27 03:38:07 INFO:  Fetched 3414 new records
2025-09-27 03:38:09 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:08:00
2025-09-27 03:38:09 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-27 03:38:09 INFO:  Saved processed file: NIFTY_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:38:09 INFO:  Fetching 13 dailymin data...
2025-09-27 03:38:09 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-27 03:38:09 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_daily.csv
2025-09-27 03:38:09 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00 for NIFTY for interval daily
2025-09-27 03:38:09 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00
2025-09-27 03:38:09 INFO:  Fetched 250 new records
2025-09-27 03:38:09 INFO:   \u2192 No zones affected by latest candle 2025-09-26 09:15:00
2025-09-27 03:38:09 INFO:  Saved processed file: NIFTY_daily_2024-09-27_to_2025-09-26_processed.csv
2025-09-27 03:38:09 INFO: Resistant zone: Demand low=24630.35, high=24674.65, status=Tested
2025-09-27 03:38:09 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-09-27 03:38:09 INFO: End date: 2025-09-26
2025-09-27 03:38:09 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL
2025-09-27 03:38:09 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_1min.csv for interval 1
2025-09-27 03:38:09 INFO: Data fetched from 2025-09-16 09:18:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 1
2025-09-27 03:38:10 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:38:10 INFO:      Saved 1min data: NIFTY_30_SEP_24850_CALL_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:38:10 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_5min.csv for interval 5
2025-09-27 03:38:10 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 5
2025-09-27 03:38:10 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:38:10 INFO:      Saved 5min data: NIFTY_30_SEP_24850_CALL_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:38:10 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:38:10 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-09-27 03:38:10 INFO: End date: 2025-09-26
2025-09-27 03:38:10 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT
2025-09-27 03:38:10 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_1min.csv for interval 1
2025-09-27 03:38:10 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 15:31:00 for NIFTY 30 SEP 24900 PUT for interval 1
2025-09-27 03:38:12 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:31:00
2025-09-27 03:38:12 INFO:      Saved 1min data: NIFTY_30_SEP_24900_PUT_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:38:12 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_5min.csv for interval 5
2025-09-27 03:38:12 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24900 PUT for interval 5
2025-09-27 03:38:13 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:38:13 INFO:      Saved 5min data: NIFTY_30_SEP_24900_PUT_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:38:13 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:38:13 INFO: State: SCANNING for trade signals.
2025-09-27 03:38:13 INFO: Flag value before look_for_trade : three_consecutive_same_trades_flag = False
2025-09-27 03:38:13 INFO: Entering function: look_for_trade_signals
2025-09-27 03:38:13 INFO: CALLS Symbol in master dataframes: NIFTY 30 SEP 24850 CALL and symbol.split: CALL
2025-09-27 03:38:13 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: None
2025-09-27 03:38:13 INFO: PUTS Symbol in master dataframes: NIFTY 30 SEP 24900 PUT and symbol.split: PUT
2025-09-27 03:38:13 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: NIFTY 30 SEP 24900 PUT
2025-09-27 03:38:13 INFO: ------------- FOR CALL ------------------
2025-09-27 03:38:13 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:25:00 
2025-09-27 03:38:13 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:38:13 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:38:13 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 14:50:00 
2025-09-27 03:38:13 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 13:15:00 
2025-09-27 03:38:13 INFO: ------------- FOR PUT ------------------
2025-09-27 03:38:13 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:18:00 
2025-09-27 03:38:13 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:38:13 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:38:13 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 15:15:00 
2025-09-27 03:38:13 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:30:00 
2025-09-27 03:38:13 INFO: First trade. No previous crossover timestamp available.
2025-09-27 03:38:13 INFO: new_crossover happened: True
2025-09-27 03:38:13 INFO: Exiting function: look_for_trade_signals (no signal found for NIFTY)
2025-09-27 03:38:13 INFO: ============================================================
2025-09-27 03:38:13 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-27 03:38:13 INFO: ============================================================

2025-09-27 03:38:13 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
2025-09-27 03:41:49 INFO: === ****************************************************** ===
2025-09-27 03:41:49 INFO: === ****************START LOGGING* (03:41:49) ************ ===
2025-09-27 03:41:49 INFO: === ****************************************************** ===
2025-09-27 03:41:49 INFO: === Algo Trading Bot Started ===
2025-09-27 03:41:49 INFO: Output for processed NIFTY data: processed_files
2025-09-27 03:41:49 INFO: Output for processed Options data: processed_options_files
2025-09-27 03:41:49 INFO: Options input file: User_options_input.csv
2025-09-27 03:41:49 INFO: ============================================================
2025-09-27 03:41:49 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-27 03:41:49 INFO: --- Processing Historical NIFTY Data (03:41:49) ---
2025-09-27 03:41:49 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-27 03:41:49 INFO:  Fetching 13 5min data...
2025-09-27 03:41:49 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-27 03:41:49 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_5min.csv
2025-09-27 03:41:49 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00 for NIFTY for interval 5
2025-09-27 03:41:49 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00
2025-09-27 03:41:49 INFO:  Fetched 1133 new records
2025-09-27 03:41:50 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:05:00
2025-09-27 03:41:50 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-27 03:41:50 INFO:  Saved processed file: NIFTY_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:41:50 INFO:  Fetching 13 1min data...
2025-09-27 03:41:50 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-27 03:41:50 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1min.csv
2025-09-27 03:41:50 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00 for NIFTY for interval 1
2025-09-27 03:41:50 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00
2025-09-27 03:41:50 INFO:  Fetched 3414 new records
2025-09-27 03:41:52 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:08:00
2025-09-27 03:41:52 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-27 03:41:52 INFO:  Saved processed file: NIFTY_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:41:52 INFO:  Fetching 13 60min data...
2025-09-27 03:41:52 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-27 03:41:52 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1hour.csv
2025-09-27 03:41:52 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00 for NIFTY for interval 60
2025-09-27 03:41:52 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00
2025-09-27 03:41:52 INFO:  Fetched 301 new records
2025-09-27 03:41:52 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:15:00
2025-09-27 03:41:52 INFO:  Saved processed file: NIFTY_60min_2025-07-28_to_2025-09-26_processed.csv
2025-09-27 03:41:52 INFO:  Fetching 13 dailymin data...
2025-09-27 03:41:52 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-27 03:41:52 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_daily.csv
2025-09-27 03:41:52 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00 for NIFTY for interval daily
2025-09-27 03:41:52 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00
2025-09-27 03:41:52 INFO:  Fetched 250 new records
2025-09-27 03:41:52 INFO:   \u2192 No zones affected by latest candle 2025-09-26 09:15:00
2025-09-27 03:41:52 INFO:  Saved processed file: NIFTY_daily_2024-09-27_to_2025-09-26_processed.csv
2025-09-27 03:41:52 INFO: Resistant zone: Demand low=24630.35, high=24674.65, status=Tested
2025-09-27 03:41:52 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-09-27 03:41:52 INFO: End date: 2025-09-26
2025-09-27 03:41:52 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL
2025-09-27 03:41:52 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_1min.csv for interval 1
2025-09-27 03:41:52 INFO: Data fetched from 2025-09-16 09:18:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 1
2025-09-27 03:41:53 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:41:53 INFO:      Saved 1min data: NIFTY_30_SEP_24850_CALL_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:41:53 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_5min.csv for interval 5
2025-09-27 03:41:53 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 5
2025-09-27 03:41:53 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:41:53 INFO:      Saved 5min data: NIFTY_30_SEP_24850_CALL_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:41:53 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:41:53 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-09-27 03:41:53 INFO: End date: 2025-09-26
2025-09-27 03:41:53 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT
2025-09-27 03:41:53 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_1min.csv for interval 1
2025-09-27 03:41:53 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 15:31:00 for NIFTY 30 SEP 24900 PUT for interval 1
2025-09-27 03:41:55 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:31:00
2025-09-27 03:41:55 INFO:      Saved 1min data: NIFTY_30_SEP_24900_PUT_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:41:55 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_5min.csv for interval 5
2025-09-27 03:41:55 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24900 PUT for interval 5
2025-09-27 03:41:56 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:41:56 INFO:      Saved 5min data: NIFTY_30_SEP_24900_PUT_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:41:56 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:41:56 INFO: State: SCANNING for trade signals.
2025-09-27 03:41:56 INFO: Flag value before look_for_trade : three_consecutive_same_trades_flag = False
2025-09-27 03:41:56 INFO: Entering function: look_for_trade_signals
2025-09-27 03:41:56 INFO: CALLS Symbol in master dataframes: NIFTY 30 SEP 24850 CALL and symbol.split: CALL
2025-09-27 03:41:56 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: None
2025-09-27 03:41:56 INFO: PUTS Symbol in master dataframes: NIFTY 30 SEP 24900 PUT and symbol.split: PUT
2025-09-27 03:41:56 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: NIFTY 30 SEP 24900 PUT
2025-09-27 03:41:56 INFO: ------------- FOR CALL ------------------
2025-09-27 03:41:56 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:25:00 
2025-09-27 03:41:56 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:41:56 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:41:56 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 14:50:00 
2025-09-27 03:41:56 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 13:15:00 
2025-09-27 03:41:56 INFO: ------------- FOR PUT ------------------
2025-09-27 03:41:56 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:18:00 
2025-09-27 03:41:56 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:41:56 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:41:56 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 15:15:00 
2025-09-27 03:41:56 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:30:00 
2025-09-27 03:41:56 INFO: First trade. No previous crossover timestamp available.
2025-09-27 03:41:56 INFO: new_crossover happened: True
2025-09-27 03:41:56 INFO: Exiting function: look_for_trade_signals (no signal found for NIFTY)
2025-09-27 03:41:56 INFO: ============================================================
2025-09-27 03:41:56 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-27 03:41:56 INFO: ============================================================

2025-09-27 03:41:56 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
2025-09-27 03:46:34 INFO: === ****************************************************** ===
2025-09-27 03:46:34 INFO: === ****************START LOGGING* (03:46:34) ************ ===
2025-09-27 03:46:34 INFO: === ****************************************************** ===
2025-09-27 03:46:34 INFO: === Algo Trading Bot Started ===
2025-09-27 03:46:34 INFO: Output for processed NIFTY data: processed_files
2025-09-27 03:46:34 INFO: Output for processed Options data: processed_options_files
2025-09-27 03:46:34 INFO: Options input file: User_options_input.csv
2025-09-27 03:46:34 INFO: ============================================================
2025-09-27 03:46:34 INFO: Sending Telegram message: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-09-27 03:46:34 INFO: --- Processing Historical NIFTY Data (03:46:34) ---
2025-09-27 03:46:34 INFO:  First run: scheduling all intervals for full historical fetch
2025-09-27 03:46:34 INFO:  Fetching 13 5min data...
2025-09-27 03:46:34 INFO: Entering function: Fetch_index_rows for interval 5 and NIFTY
2025-09-27 03:46:34 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_5min.csv
2025-09-27 03:46:34 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00 for NIFTY for interval 5
2025-09-27 03:46:34 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 16:05:00
2025-09-27 03:46:34 INFO:  Fetched 1133 new records
2025-09-27 03:46:34 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:05:00
2025-09-27 03:46:34 INFO: No 5 minute zones affected. Zones_affected_5 is empty
2025-09-27 03:46:34 INFO:  Saved processed file: NIFTY_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:46:34 INFO:  Fetching 13 60min data...
2025-09-27 03:46:34 INFO: Entering function: Fetch_index_rows for interval 60 and NIFTY
2025-09-27 03:46:34 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1hour.csv
2025-09-27 03:46:34 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00 for NIFTY for interval 60
2025-09-27 03:46:34 INFO: Data fetched from 2025-07-28 09:15:00 to 2025-09-26 15:15:00
2025-09-27 03:46:34 INFO:  Fetched 301 new records
2025-09-27 03:46:35 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:15:00
2025-09-27 03:46:35 INFO:  Saved processed file: NIFTY_60min_2025-07-28_to_2025-09-26_processed.csv
2025-09-27 03:46:35 INFO:  Fetching 13 1min data...
2025-09-27 03:46:35 INFO: Entering function: Fetch_index_rows for interval 1 and NIFTY
2025-09-27 03:46:35 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_1min.csv
2025-09-27 03:46:35 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00 for NIFTY for interval 1
2025-09-27 03:46:35 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 16:08:00
2025-09-27 03:46:35 INFO:  Fetched 3414 new records
2025-09-27 03:46:36 INFO:   \u2192 No zones affected by latest candle 2025-09-26 16:08:00
2025-09-27 03:46:36 INFO: No 1 minute zones affected. Zones_affected_1 is empty
2025-09-27 03:46:36 INFO:  Saved processed file: NIFTY_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:46:36 INFO:  Fetching 13 dailymin data...
2025-09-27 03:46:36 INFO: Entering function: Fetch_index_rows for interval daily and NIFTY
2025-09-27 03:46:36 INFO: Input file for index: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\index_logs\2025-09-26\NIFTY\nifty_candles_daily.csv
2025-09-27 03:46:36 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00 for NIFTY for interval daily
2025-09-27 03:46:36 INFO: Data fetched from 2024-09-27 00:00:00 to 2025-09-26 09:15:00
2025-09-27 03:46:36 INFO:  Fetched 250 new records
2025-09-27 03:46:36 INFO:   \u2192 No zones affected by latest candle 2025-09-26 09:15:00
2025-09-27 03:46:36 INFO:  Saved processed file: NIFTY_daily_2024-09-27_to_2025-09-26_processed.csv
2025-09-27 03:46:36 INFO: Resistant zone: Demand low=24630.35, high=24674.65, status=Tested
2025-09-27 03:46:36 INFO: Entering function: Fetch_options_rows for CALL and NIFTY
2025-09-27 03:46:36 INFO: End date: 2025-09-26
2025-09-27 03:46:36 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL
2025-09-27 03:46:36 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_1min.csv for interval 1
2025-09-27 03:46:36 INFO: Data fetched from 2025-09-16 09:18:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 1
2025-09-27 03:46:37 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:46:37 INFO:      Saved 1min data: NIFTY_30_SEP_24850_CALL_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:46:37 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24850_CALL\nifty_30_sep_24850_call_candles_5min.csv for interval 5
2025-09-27 03:46:37 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24850 CALL for interval 5
2025-09-27 03:46:38 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:46:38 INFO:      Saved 5min data: NIFTY_30_SEP_24850_CALL_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:46:38 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:46:38 INFO: Entering function: Fetch_options_rows for PUT and NIFTY
2025-09-27 03:46:38 INFO: End date: 2025-09-26
2025-09-27 03:46:38 INFO: Input directory for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT
2025-09-27 03:46:38 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_1min.csv for interval 1
2025-09-27 03:46:38 INFO: Data fetched from 2025-09-16 09:15:00 to 2025-09-26 15:31:00 for NIFTY 30 SEP 24900 PUT for interval 1
2025-09-27 03:46:40 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:31:00
2025-09-27 03:46:40 INFO:      Saved 1min data: NIFTY_30_SEP_24900_PUT_1min_2025-09-16_to_2025-09-26_processed.csv
2025-09-27 03:46:40 INFO: Input file for options: C:\Users\<USER>\Documents\Trading\India trading strategies\Index_option_streamers\options_logs\2025-09-26\NIFTY_30_SEP_24900_PUT\nifty_30_sep_24900_put_candles_5min.csv for interval 5
2025-09-27 03:46:40 INFO: Data fetched from 2025-09-08 09:15:00 to 2025-09-26 15:30:00 for NIFTY 30 SEP 24900 PUT for interval 5
2025-09-27 03:46:40 INFO:   \u2192 No zones affected by latest candle 2025-09-26 15:30:00
2025-09-27 03:46:40 INFO:      Saved 5min data: NIFTY_30_SEP_24900_PUT_5min_2025-09-08_to_2025-09-26_processed.csv
2025-09-27 03:46:40 INFO: Options processing completed: 0 successful, 0 failed
2025-09-27 03:46:40 INFO: State: SCANNING for trade signals.
2025-09-27 03:46:40 INFO: Flag value before look_for_trade : three_consecutive_same_trades_flag = False
2025-09-27 03:46:40 INFO: Entering function: look_for_trade_signals
2025-09-27 03:46:40 INFO: CALLS Symbol in master dataframes: NIFTY 30 SEP 24850 CALL and symbol.split: CALL
2025-09-27 03:46:40 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: None
2025-09-27 03:46:40 INFO: PUTS Symbol in master dataframes: NIFTY 30 SEP 24900 PUT and symbol.split: PUT
2025-09-27 03:46:40 INFO: call_option_symbol: NIFTY 30 SEP 24850 CALL and put_option_symbol: NIFTY 30 SEP 24900 PUT
2025-09-27 03:46:40 INFO: ------------- FOR CALL ------------------
2025-09-27 03:46:40 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:25:00 
2025-09-27 03:46:40 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:46:40 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:09:00 
2025-09-27 03:46:40 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 14:50:00 
2025-09-27 03:46:40 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 13:15:00 
2025-09-27 03:46:40 INFO: ------------- FOR PUT ------------------
2025-09-27 03:46:40 INFO: At begining- 1-min crossover_signal_5_13: downward timestamp: 2025-09-26 15:18:00 
2025-09-27 03:46:40 INFO: At begining- 1-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:46:40 INFO: At begining- 1-min crossover_signal_9_45: downward timestamp: 2025-09-26 15:21:00 
2025-09-27 03:46:40 INFO: At begining- 5-min latest crossover: downward timestamp: 2025-09-26 15:15:00 
2025-09-27 03:46:40 INFO: At begining- 5-min crossover_signal_9_26: downward timestamp: 2025-09-26 15:30:00 
2025-09-27 03:46:40 INFO: First trade. No previous crossover timestamp available.
2025-09-27 03:46:40 INFO: new_crossover happened: True
2025-09-27 03:46:40 INFO: Exiting function: look_for_trade_signals (no signal found for NIFTY)
2025-09-27 03:46:40 INFO: ============================================================
2025-09-27 03:46:40 INFO: Current State: SCANNING. Next update in 5 seconds...
2025-09-27 03:46:40 INFO: ============================================================

2025-09-27 03:46:40 INFO: Sending Telegram message: ===Algo_Trading===$INTIMATION$Premarket time now for NIFTY. Sleeping for 10 min.
