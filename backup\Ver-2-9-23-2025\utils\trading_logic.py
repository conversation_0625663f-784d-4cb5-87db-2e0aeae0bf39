# trading_logic.py

import datetime
import logging
import os
import pandas as pd

import config.config as config
from utils.tele_bot import send_msg_to_telegram
from utils.processing_functions import get_resistant_zones
from utils.processing_functions import identify_zone_on_zone
from utils.processing_functions import check_zone_over_zone

import warnings
# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')


def find_latest_signals(df):
    """
    Analyze a processed option DataFrame to identify the most recent trading signals.
    """
    result = {'latest_crossover': None, 'latest_zone': None}
    if df is None or df.empty:
        return result

    required_cols = ['timestamp', 'crossover_signal', 'Zones_sup_dem', 'Zone_Status']
    if not all(col in df.columns for col in required_cols):
        return result

    try:
        mask_crossover = df['crossover_signal'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover'] = df.loc[last_idx]
    except Exception:
        pass

    try:
        mask_zone = (df['Zones_sup_dem'].isin(['Supply', 'Demand']) &
                     df['Zones_sup_dem'].notna() &
                     (df['Zones_sup_dem'].astype(str).str.strip() != '') &
                     df['Zone_Status'].isin(['Valid', 'Tested']))
        if mask_zone.any():
            last_idx = df[mask_zone].index[-1]
            prior_to_last_idx = df[mask_zone].index[-2]
            result['latest_zone'] = df.loc[last_idx]
            result['prior_to_latest_zone'] = df.loc[prior_to_last_idx]
            two_prior_to_last_idx = df[mask_crossover].index[-3]
            result['two_prior_to_latest_zone'] = df.loc[two_prior_to_last_idx]
    except Exception:
        pass
    
    return result

def find_latest_other_signals(df=pd.DataFrame()):
    """
    Analyze a processed option DataFrame to identify the most recent trading signals.
    """
    result = {
        'latest_crossover_225_325': {'timestamp': None, 'crossover_signal_225_325': None}, 
        'latest_crossover_45_325': {'timestamp': None, 'crossover_signal_45_325': None},
        'latest_crossover_45_225': {'timestamp': None, 'crossover_signal_45_225': None},
        'latest_crossover_9_65': {'timestamp': None, 'crossover_signal_9_65': None},
        'latest_crossover_9_45': {'timestamp': None, 'crossover_signal_9_45': None},
        'latest_crossover_9_26': {'timestamp': None, 'crossover_signal_9_26': None},
        'latest_crossover_9_90': {'timestamp': None, 'crossover_signal_9_90': None},
        'latest_crossover_45_65': {'timestamp': None, 'crossover_signal_45_65': None},
        'latest_crossover_5_9': {'timestamp': None, 'crossover_signal_5_9': None}
    }
    
    if df is None or df.empty:
        return result

    required_cols = ['timestamp', 'crossover_signal_5_9', 'crossover_signal_9_26', 
                    'crossover_signal_45_65', 'crossover_signal_9_45', 'crossover_signal_9_65',
                    'crossover_signal_225_325', 'crossover_signal_45_225', 'crossover_signal_45_325']
    if not all(col in df.columns for col in required_cols):
        return result
    
    try:
        mask_crossover = df['crossover_signal_5_9'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_5_9'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_5_9': df.loc[last_idx, 'crossover_signal_5_9']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_5_9: {e}")
    
    try:
        mask_crossover = df['crossover_signal_9_26'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_9_26'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_9_26': df.loc[last_idx, 'crossover_signal_9_26']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_9_26: {e}")

    try:
        mask_crossover = df['crossover_signal_45_65'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_45_65'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_45_65': df.loc[last_idx, 'crossover_signal_45_65']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_45_65: {e}")

    try:
        mask_crossover = df['crossover_signal_9_45'].isin(['upward', 'downward'])   
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_9_45'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_9_45': df.loc[last_idx, 'crossover_signal_9_45']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_9_45: {e}")

    try:
        mask_crossover = df['crossover_signal_9_65'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_9_65'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_9_65': df.loc[last_idx, 'crossover_signal_9_65']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_9_65: {e}")

    try:
        mask_crossover = df['crossover_signal_9_90'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_9_90'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_9_90': df.loc[last_idx, 'crossover_signal_9_90']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_9_90: {e}")

    try:
        mask_crossover = df['crossover_signal_225_325'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_225_325'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_225_325': df.loc[last_idx, 'crossover_signal_225_325']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_225_325: {e}")

    try:
        mask_crossover = df['crossover_signal_45_225'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_45_225'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_45_225': df.loc[last_idx, 'crossover_signal_45_225']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_45_225: {e}")

    try:
        mask_crossover = df['crossover_signal_45_325'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover_45_325'] = {
                'timestamp': df.loc[last_idx, 'timestamp'],
                'crossover_signal_45_325': df.loc[last_idx, 'crossover_signal_45_325']
            }
    except Exception as e:
        logging.error(f"Error processing crossover_signal_45_325: {e}")
    
    # result = {'latest_crossover_225_325': None, 
    #           'latest_crossover_45_325': None,
    #           'latest_crossover_45_225': None,
    #           'latest_crossover_9_65': None,
    #           'latest_crossover_9_45': None,
    #           'latest_crossover_9_26': None,
    #           'latest_crossover_9_90': None,
    #           'latest_crossover_45_65': None,
    #           'latest_crossover_5_9': None
    #           }
    
    # if df is None or df.empty:
    #     return result

    # required_cols = ['timestamp', 'crossover_signal_5_9', 'crossover_signal_9_26', 'crossover_signal_45_65', 'crossover_signal_9_45', 'crossover_signal_9_65', 'crossover_signal_225_325', 'crossover_signal_45_225', 'crossover_signal_45_325']
    # if not all(col in df.columns for col in required_cols):
    #     return result
    
    # try:
    #     mask_crossover = df['crossover_signal_5_9'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_5_9'] = df.loc[last_idx]
    # except Exception:
    #     pass

    # try:
    #     mask_crossover = df['crossover_signal_9_26'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_9_26'] = df.loc[last_idx]
    # except Exception:
    #     pass

    # try:
    #     mask_crossover = df['crossover_signal_45_65'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_45_65'] = df.loc[last_idx]
    # except Exception:
    #     pass

    # try:
    #     mask_crossover = df['crossover_signal_9_45'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_9_45'] = df.loc[last_idx]
    # except Exception:
    #     pass

    # try:
    #     mask_crossover = df['crossover_signal_9_65'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_9_65'] = df.loc[last_idx]
    # except Exception:
    #     pass

    # try:
    #     mask_crossover = df['crossover_signal_9_90'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_9_90'] = df.loc[last_idx]
    # except Exception:
    #     pass

    # try:
    #     mask_crossover = df['crossover_signal_225_325'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_225_325'] = df.loc[last_idx]
    # except Exception:
    #     pass

    # try:
    #     mask_crossover = df['crossover_signal_45_225'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_45_225'] = df.loc[last_idx]
    # except Exception:
    #     pass

    # try:
    #     mask_crossover = df['crossover_signal_45_325'].isin(['upward', 'downward'])
    #     if mask_crossover.any():
    #         last_idx = df[mask_crossover].index[-1]
    #         result['latest_crossover_45_325'] = df.loc[last_idx] 
    # except Exception:
    #     pass
    
    return result

def look_for_trade_signals(df_1, df_5, trade_info=pd.DataFrame(), instrument_name=None, Zones_affected_1=None, Zones_affected_5=None):
    """
    Looks for trade signals based on 1-min and 5-min data.
    """
    logging.info("Entering function: look_for_trade_signals")
    if df_1 is None or df_5 is None or df_1.empty or df_5.empty:
        logging.info("Exiting look_for_trade_signals: Input DataFrame is empty.")
        return "None", 'False', trade_info

    latest_1min = find_latest_signals(df_1)
    latest_other_signals_1min = find_latest_other_signals(df_1)
    latest_5min = find_latest_signals(df_5)
    
    # Check if any of the required signals are None
    for signal_key in ['latest_crossover_5_9', 'latest_crossover_45_65', 
                      'latest_crossover_9_45', 'latest_crossover_9_65',
                      'latest_crossover_225_325', 'latest_crossover_45_225',
                      'latest_crossover_45_325']:
        if (latest_other_signals_1min[signal_key] is None or
            'timestamp' not in latest_other_signals_1min[signal_key]):
            logging.info(f"Exiting look_for_trade_signals: Signal {signal_key} not found or invalid")
            print(f"Exiting look_for_trade_signals: Signal {signal_key} not found or invalid")
            return "None", 'False', trade_info

    # Now it's safe to log the timestamps
    logging.info(f"latest_other_signals_1min['latest_crossover_5_9']: {latest_other_signals_1min['latest_crossover_5_9']['timestamp']}")
    logging.info(f"latest_other_signals_1min['latest_crossover_45_65']: {latest_other_signals_1min['latest_crossover_45_65']['timestamp']}")
    logging.info(f"latest_other_signals_1min['latest_crossover_9_45']: {latest_other_signals_1min['latest_crossover_9_45']['timestamp']}")
    logging.info(f"latest_other_signals_1min['latest_crossover_9_65']: {latest_other_signals_1min['latest_crossover_9_65']['timestamp']}")
    logging.info(f"latest_other_signals_1min['latest_crossover_225_325']: {latest_other_signals_1min['latest_crossover_225_325']['timestamp']}")
    logging.info(f"latest_other_signals_1min['latest_crossover_45_225']: {latest_other_signals_1min['latest_crossover_45_225']['timestamp']}")
    logging.info(f"latest_other_signals_1min['latest_crossover_45_325']: {latest_other_signals_1min['latest_crossover_45_325']['timestamp']}")


    # if (latest_other_signals_1min['latest_crossover_5_9'] is None or
    #     latest_other_signals_1min['latest_crossover_45_65'] is None or
    #     latest_other_signals_1min['latest_crossover_9_45'] is None or
    #     latest_other_signals_1min['latest_crossover_9_65'] is None or
    #     latest_other_signals_1min['latest_crossover_225_325'] is None or
    #     latest_other_signals_1min['latest_crossover_45_225'] is None or
    #     latest_other_signals_1min['latest_crossover_45_325'] is None):
    #     logging.info("Exiting look_for_trade_signals: One or more signals not found.")
    #     logging.info(f"latest_other_signals_1min['latest_crossover_5_9']: {latest_other_signals_1min['latest_crossover_5_9']['timestamp']}")
    #     logging.info(f"latest_other_signals_1min['latest_crossover_45_65']: {latest_other_signals_1min['latest_crossover_45_65']['timestamp']}")
    #     logging.info(f"latest_other_signals_1min['latest_crossover_9_45']: {latest_other_signals_1min['latest_crossover_9_45']['timestamp']}")
    #     logging.info(f"latest_other_signals_1min['latest_crossover_9_65']: {latest_other_signals_1min['latest_crossover_9_65']['timestamp']}")
    #     logging.info(f"latest_other_signals_1min['latest_crossover_225_325']: {latest_other_signals_1min['latest_crossover_225_325']['timestamp']}")
    #     logging.info(f"latest_other_signals_1min['latest_crossover_45_225']: {latest_other_signals_1min['latest_crossover_45_225']['timestamp']}")
    #     logging.info(f"latest_other_signals_1min['latest_crossover_45_325']: {latest_other_signals_1min['latest_crossover_45_325']['timestamp']}")
    #     print("Exiting look_for_trade_signals: One or more signals not found.")
    #     return "None", 'False', trade_info
    
    last_stored_1min_data = df_1.iloc[-1]
    last_stored_5min_data = df_5.iloc[-1]
    Zone_on_5min_zone = False
    Zone_on_1min_zone = False
    entry_reason = "None"
    
    is_put_signal = False
    is_put_sig1 = False
    is_put_sig2 = False
    is_put_sig3 = False
    is_put_sig4 = False
    is_put_sig5 = False

    is_call_signal = False
    is_call_sig1 = False
    is_call_sig2 = False
    is_call_sig3 = False
    is_call_sig4 = False
    is_call_sig5 = False
    
    logging.info(f"last stored timestamp: {last_stored_1min_data['timestamp']}")
    logging.info(f"At begining- 1-min crossover_signal_5_9: {latest_other_signals_1min['latest_crossover_5_9']['crossover_signal_5_9']} timestamp: {latest_other_signals_1min['latest_crossover_5_9']['timestamp']}")
    logging.info(f"At begining- 1-min latest crossover: {latest_1min['latest_crossover']['crossover_signal']} timestamp: {latest_1min['latest_crossover']['timestamp']}")
    logging.info(f"At begining- 1-min crossover_signal_45_65: {latest_other_signals_1min['latest_crossover_45_65']['crossover_signal_45_65']} timestamp: {latest_other_signals_1min['latest_crossover_45_65']['timestamp']}")
    logging.info(f"At begining- 1-min crossover_signal_9_45: {latest_other_signals_1min['latest_crossover_9_45']['crossover_signal_9_45']} timestamp: {latest_other_signals_1min['latest_crossover_9_45']['timestamp']}")
    logging.info(f"At begining- 1-min crossover_signal_9_65: {latest_other_signals_1min['latest_crossover_9_65']['crossover_signal_9_65']} timestamp: {latest_other_signals_1min['latest_crossover_9_65']['timestamp']}")
    logging.info(f"At begining- 1-min crossover_signal_9_90: {latest_other_signals_1min['latest_crossover_9_90']['crossover_signal_9_90']} timestamp: {latest_other_signals_1min['latest_crossover_9_90']['timestamp']}")
    logging.info(f"At begining- 1-min crossover_signal_225_325: {latest_other_signals_1min['latest_crossover_225_325']['crossover_signal_225_325']} timestamp: {latest_other_signals_1min['latest_crossover_225_325']['timestamp']}")
    logging.info(f"At begining- 1-min crossover_signal_45_225: {latest_other_signals_1min['latest_crossover_45_225']['crossover_signal_45_225']} timestamp: {latest_other_signals_1min['latest_crossover_45_225']['timestamp']}")
    logging.info(f"At begining- 1-min crossover_signal_45_325: {latest_other_signals_1min['latest_crossover_45_325']['crossover_signal_45_325']} timestamp: {latest_other_signals_1min['latest_crossover_45_325']['timestamp']}")
    logging.info(f"At begining- 5-min latest crossover: {latest_5min['latest_crossover']['crossover_signal']} timestamp: {latest_5min['latest_crossover']['timestamp']}")

    new_crossover = False
    print(f"Data type of trade_info: {type(trade_info)}")
    if trade_info != {}:
        if latest_1min['latest_crossover']['timestamp'] > trade_info['last_trade_crossover_timestamp']:
            logging.info(f"New crossover happened after last trade. {latest_1min['latest_crossover']['timestamp']}")
            new_crossover = True
    else:
        logging.info(f"First trade. No previous crossover timestamp available.")
        new_crossover = True
    
    logging.info(f"new_crossover happened: {new_crossover}")
    if new_crossover == True:
        #Identify if the current crossover is a zone on zone
        logging.info(f"Identifying zone on zone for 5 min and 1 min: latest zone: {latest_1min['latest_zone']['timestamp']} {latest_1min['latest_zone']['Zones_sup_dem']} status: {latest_1min['latest_zone']['Zone_Status']}")   
        df_zone_on_zone = identify_zone_on_zone(df_5, latest_1min['latest_zone'], latest_1min['latest_zone']['Zones_sup_dem'])
        if not df_zone_on_zone.empty:
            Zone_on_5min_zone = True
            logging.info(f"Found zone on zone for 5 min: {df_zone_on_zone.iloc[0]}")
        else:
            df_zone_on_zone = identify_zone_on_zone(df_1, latest_1min['latest_zone'], latest_1min['latest_zone']['Zones_sup_dem'])
            if not df_zone_on_zone.empty:
                Zone_on_1min_zone = True
                logging.info(f"Found zone on zone for 1 min: {df_zone_on_zone.iloc[0]}")
        logging.info(f"Zone_on_5min_zone: {Zone_on_5min_zone}, Zone_on_1min_zone: {Zone_on_1min_zone}")
        #Print all entries of df_zone_on_zone
        if df_zone_on_zone is not None and not df_zone_on_zone.empty:
            for idx, row in df_zone_on_zone.iterrows():
                logging.info(f"Zone on Zone Entry {idx}:")
                logging.info(row)
                logging.info("-" * 40)
        else:
            logging.info("No zone-on-zone entries found.")

        if (latest_other_signals_1min['latest_crossover_45_65']['crossover_signal_45_65'] == 'upward' and
            latest_other_signals_1min['latest_crossover_9_65']['crossover_signal_9_65'] == 'upward' and
            latest_other_signals_1min['latest_crossover_9_45']['crossover_signal_9_45'] == 'upward' and
            latest_other_signals_1min['latest_crossover_5_9']['crossover_signal_5_9'] == 'upward' and 
            latest_other_signals_1min['latest_crossover_9_90']['crossover_signal_9_90'] == 'upward' and
            latest_1min['latest_crossover']['crossover_signal'] == 'upward' and
            latest_5min['latest_crossover']['crossover_signal'] == 'upward'
            ):
            logging.info(f"difference between last stored and 5_9 : {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_5_9']['timestamp'])}")
            logging.info(f"difference between last stored and 5_13: {(last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp'])}")
            logging.info(f"difference between last stored and 45_65: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_45_65']['timestamp'])}")
            logging.info(f"difference between last stored and 9_45: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_9_45']['timestamp'])}")
            logging.info(f"difference between last stored and 9_65: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_9_65']['timestamp'])}")
            logging.info(f"difference between last stored and 225_325: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_225_325']['timestamp'])}")
            logging.info(f"difference between last stored and 45_225: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_45_225']['timestamp'])}")
            logging.info(f"difference between last stored and 45_325: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_45_325']['timestamp'])}")
            
            is_call_sig1 = ((last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_5_9']['timestamp']) <= datetime.timedelta(minutes=3) and
                            (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_other_signals_1min['latest_crossover_5_9']['timestamp'] >= latest_1min['latest_crossover']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_5_9']['timestamp'] >= latest_other_signals_1min['latest_crossover_45_65']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_5_9']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_45']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_5_9']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_65']['timestamp']
                            )
            
            is_call_sig2 = ((last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_1min['latest_crossover']['timestamp'] >= latest_other_signals_1min['latest_crossover_5_9']['timestamp']  and
                            latest_1min['latest_crossover']['timestamp'] >= latest_other_signals_1min['latest_crossover_45_65']['timestamp'] and
                            latest_1min['latest_crossover']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_45']['timestamp'] and
                            latest_1min['latest_crossover']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_65']['timestamp']
                            )
            
            is_call_sig3 = ((last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_45_65']['timestamp']) <= datetime.timedelta(minutes=3) and
                            (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_other_signals_1min['latest_crossover_45_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_5_9']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_45_65']['timestamp'] >= latest_1min['latest_crossover']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_45_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_45']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_45_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_65']['timestamp'] 
                            )
            
            is_call_sig4 = ((last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_9_45']['timestamp']) <= datetime.timedelta(minutes=3) and
                            (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_other_signals_1min['latest_crossover_9_45']['timestamp'] >= latest_other_signals_1min['latest_crossover_5_9']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_45']['timestamp'] >= latest_other_signals_1min['latest_crossover_45_65']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_45']['timestamp'] >= latest_1min['latest_crossover']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_45']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_65']['timestamp'] 
                            )
            
            is_call_sig5 = ((last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_9_65']['timestamp']) <= datetime.timedelta(minutes=3) and
                            (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_other_signals_1min['latest_crossover_9_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_5_9']['timestamp'] and   
                            latest_other_signals_1min['latest_crossover_9_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_45_65']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_45']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_65']['timestamp'] >= latest_1min['latest_crossover']['timestamp'] 
                            )
        is_call_signal = is_call_sig1 or is_call_sig2 or is_call_sig3 or is_call_sig4 or is_call_sig5
        logging.info(f"After checking for call signal flag value is : {is_call_signal}")

        if (latest_other_signals_1min['latest_crossover_45_65']['crossover_signal_45_65'] == 'downward' and
            latest_other_signals_1min['latest_crossover_9_65']['crossover_signal_9_65'] == 'downward' and
            latest_other_signals_1min['latest_crossover_9_45']['crossover_signal_9_45'] == 'downward' and
            latest_other_signals_1min['latest_crossover_5_9']['crossover_signal_5_9'] == 'downward' and 
            latest_other_signals_1min['latest_crossover_9_90']['crossover_signal_9_90'] == 'downward' and
            latest_1min['latest_crossover']['crossover_signal'] == 'downward' and
            latest_5min['latest_crossover']['crossover_signal'] == 'downward'
            ):
            logging.info(f"difference between last stored and 5_9 : {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_5_9']['timestamp'])}")
            logging.info(f"difference between last stored and 5_13: {(last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp'])}")
            logging.info(f"difference between last stored and 45_65: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_45_65']['timestamp'])}")
            logging.info(f"difference between last stored and 9_45: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_9_45']['timestamp'])}")
            logging.info(f"difference between last stored and 9_65: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_9_65']['timestamp'])}")
            logging.info(f"difference between last stored and 225_325: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_225_325']['timestamp'])}")
            logging.info(f"difference between last stored and 45_225: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_45_225']['timestamp'])}")
            logging.info(f"difference between last stored and 45_325: {(last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_45_325']['timestamp'])}")
            
            is_put_sig1 = ((last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_5_9']['timestamp']) <= datetime.timedelta(minutes=3) and
                        (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_other_signals_1min['latest_crossover_5_9']['timestamp'] >= latest_1min['latest_crossover']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_5_9']['timestamp'] >= latest_other_signals_1min['latest_crossover_45_65']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_5_9']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_45']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_5_9']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_65']['timestamp'] 
                            )
            
            is_put_sig2 = ((last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_1min['latest_crossover']['timestamp'] >= latest_other_signals_1min['latest_crossover_5_9']['timestamp']  and
                            latest_1min['latest_crossover']['timestamp'] >= latest_other_signals_1min['latest_crossover_45_65']['timestamp'] and
                            latest_1min['latest_crossover']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_45']['timestamp'] and
                            latest_1min['latest_crossover']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_65']['timestamp'] 
                            )
            
            is_put_sig3 = ((last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_45_65']['timestamp']) <= datetime.timedelta(minutes=3) and
                            (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_other_signals_1min['latest_crossover_45_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_5_9']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_45_65']['timestamp'] >= latest_1min['latest_crossover']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_45_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_45']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_45_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_65']['timestamp']
                            )
            
            is_put_sig4 = ((last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_9_45']['timestamp']) <= datetime.timedelta(minutes=3) and
                            (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_other_signals_1min['latest_crossover_9_45']['timestamp'] >= latest_other_signals_1min['latest_crossover_5_9']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_45']['timestamp'] >= latest_other_signals_1min['latest_crossover_45_65']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_45']['timestamp'] >= latest_1min['latest_crossover']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_45']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_65']['timestamp'] 
                            )
            
            is_put_sig5 = ((last_stored_1min_data['timestamp'] - latest_other_signals_1min['latest_crossover_9_65']['timestamp']) <= datetime.timedelta(minutes=3) and
                            (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) <= datetime.timedelta(minutes=5) and
                            latest_other_signals_1min['latest_crossover_9_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_5_9']['timestamp'] and   
                            latest_other_signals_1min['latest_crossover_9_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_45_65']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_65']['timestamp'] >= latest_other_signals_1min['latest_crossover_9_45']['timestamp'] and
                            latest_other_signals_1min['latest_crossover_9_65']['timestamp'] >= latest_1min['latest_crossover']['timestamp'] 
                            )
        is_put_signal = is_put_sig1 or is_put_sig2 or is_put_sig3 or is_put_sig4 or is_put_sig5
        logging.info(f"After checking for put signal flag value is : {is_put_signal}")

        #if is_call_signal or is_put_signal:
        logging.info(f"1-min high: {last_stored_1min_data['high']} and low: {last_stored_1min_data['low']}")
        logging.info(f"1-min wma5: {last_stored_1min_data['WMA5']} and WMA13: {last_stored_1min_data['WMA13']} and wma90: {last_stored_1min_data['WMA90']}")
        logging.info(f"5-min high: {last_stored_5min_data['high']} and low: {last_stored_5min_data['low']}")
        logging.info(f"5-min wma5: {last_stored_5min_data['WMA5']} and WMA13: {last_stored_5min_data['WMA13']}")

        if (is_call_signal and
            last_stored_1min_data["WMA5"] > last_stored_1min_data["WMA13"] and 
            #last_stored_5min_data["low"] > last_stored_5min_data["WMA13"] and
            last_stored_1min_data["low"] > last_stored_1min_data["WMA13"]
            ):   
            if is_call_sig1:
                logging.info("CALLS REASON: because 5 X 9 happened with all other upward crossovers")
                entry_reason = "Regular_Entry_5_X_9"
            elif is_call_sig2:
                logging.info("CALLS REASON: because 5 X 13 happened with all other upward crossovers")
                entry_reason = "Regular_Entry_5_X_13"
            elif is_call_sig3:
                logging.info("CALLS REASON: because 45 X 65 happened with all other upward crossovers")
                entry_reason = "Regular_Entry_45_X_65"
            elif is_call_sig4:
                logging.info("CALLS REASON: because 9 X 45 happened with all other upward crossovers")
                entry_reason = "Regular_Entry_9_X_45"
            elif is_call_sig5:
                logging.info("CALLS REASON: because 9 X 65 happened with all other upward crossovers")
                entry_reason = "Regular_Entry_9_X_65"
            
            if (latest_other_signals_1min['latest_crossover_225_325']['crossover_signal_225_325'] == 'upward' or
                Zone_on_5min_zone == True or
                Zone_on_1min_zone == True
                ):
                logging.info("Either 225 X 325 has happened, or Zone on Zone that's why STRONG entry")
                entry_reason = "STRONG" + entry_reason
            # #Check if the current candle has hit any supply zone
            # hit_the_Supply_zone = False
            # if Zones_affected_1 is not None and not Zones_affected_1.empty:
            #     #loop through each row of Zones_affected_1 to check if any row has Zones_sup_dem as Supply
            #     logging.info("Inside look_for_trade_signals Zones Affected for 1 minute")
            #     for idx, row in Zones_affected_1.iterrows():
            #         if row['Zones_sup_dem'] == 'Supply':
            #             hit_the_Supply_zone = True
            #             logging.info(f"Supply row affected Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")

            # if Zones_affected_5 is not None and not Zones_affected_5.empty:
            #     #loop through each row of Zones_affected_5 to check if any row has Zones_sup_dem as Supply
            #     logging.info("Inside look_for_trade_signals Zones Affected for 5 minute")
            #     for idx, row in Zones_affected_5.iterrows():
            #         if row['Zones_sup_dem'] == 'Supply':
            #             hit_the_Supply_zone = True
            #             logging.info(f"Supply row affected Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")                

            # if hit_the_Supply_zone == False:
            Zone_over_Zone_1min = False
            if (Zone_on_5min_zone == False and 
                Zone_on_1min_zone == False and 
                not entry_reason.startswith("STRONG")):
                Zone_over_Zone_1min = check_zone_over_zone(df_1, "CALL", latest_1min['latest_zone'], latest_1min['prior_to_latest_zone'], latest_1min['two_prior_to_latest_zone'])
                if Zone_over_Zone_1min == True:
                    entry_reason = "STRONG" + entry_reason

            hit_the_Supply_zone = False
            #Don't check if Zones affected when it is zone on zone or zone over zone
            if Zone_on_5min_zone == False and Zone_on_1min_zone == False and Zone_over_Zone_1min == False and not entry_reason.startswith("STRONG"):
                if Zones_affected_1 is not None and not Zones_affected_1.empty:
                    #loop through each row of Zones_affected_1 to check if any row has Zones_sup_dem as Supply
                    logging.info("Inside look_for_trade Zones Affected for 1 minute")
                    for idx, row in Zones_affected_1.iterrows():
                        if row['Zones_sup_dem'] == 'Supply':
                            hit_the_Supply_zone = True
                            logging.info(f"Supply row affected Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")

                if Zones_affected_5 is not None and not Zones_affected_5.empty:
                    #loop through each row of Zones_affected_5 to check if any row has Zones_sup_dem as Supply
                    logging.info("Inside look_for_trade Zones Affected for 5 minute")
                    for idx, row in Zones_affected_5.iterrows():
                        if row['Zones_sup_dem'] == 'Supply':
                            hit_the_Supply_zone = True
                            logging.info(f"Supply row affected Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")                
            
            if hit_the_Supply_zone == False:
                logging.info("CALL signal identified. -1 ")
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$INTIMATION$Get ready for {instrument_name} CALL option trade."
                send_msg_to_telegram(message_to_send)
                trade_info['entry_reason'] = entry_reason
                trade_info['last_trade_crossover_timestamp'] = latest_1min['latest_crossover']['timestamp']                
                return "CALL", 'True', trade_info

        if (is_put_signal and
            last_stored_1min_data["WMA5"] < last_stored_1min_data["WMA13"] and 
            #last_stored_5min_data["high"] < last_stored_5min_data["WMA13"] and
            last_stored_1min_data["high"] < last_stored_1min_data["WMA13"]
            ):

            if is_put_sig1:
                logging.info("PUTS REASON: because 5 X 9 happened with all other downward crossovers")
                entry_reason = "Regular_Entry_5_X_9"
            elif is_put_sig2:
                logging.info("PUTS REASON: because 5 X 13 happened with all other downward crossovers")
                entry_reason = "Regular_Entry_5_X_13"
            elif is_put_sig3:
                logging.info("PUTS REASON: because 45 X 65 happened with all other downward crossovers")
                entry_reason = "Regular_Entry_45_X_65"
            elif is_put_sig4:
                logging.info("PUTS REASON: because 9 X 45 happened with all other downward crossovers")
                entry_reason = "Regular_Entry_9_X_45"
            elif is_put_sig5:
                logging.info("PUTS REASON: because 9 X 65 happened with all other downward crossovers")
                entry_reason = "Regular_Entry_9_X_65"
            
            if (latest_other_signals_1min['latest_crossover_225_325']['crossover_signal_225_325'] == 'downward' or
                Zone_on_5min_zone == True or
                Zone_on_1min_zone == True
                ):
                logging.info("Either 225 X 325 has happened, or Zone on Zone that's why STRONG entry")
                entry_reason = "STRONG" + entry_reason

            # #Check if the current candle has hit any demand zone
            # hit_the_Demand_zone = False
            # if Zones_affected_1 is not None and not Zones_affected_1.empty:
            #     #loop through each row of Zones_affected_1 to check if any row has Zones_sup_dem as Demand
            #     logging.info("Inside look_for_trade_signals Zones Affected for 1 minute")
            #     for idx, row in Zones_affected_1.iterrows():
            #         if row['Zones_sup_dem'] == 'Demand':
            #             hit_the_Demand_zone = True
            #             logging.info(f"Demand row affected Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")

            # if Zones_affected_5 is not None and not Zones_affected_5.empty:
            #     #loop through each row of Zones_affected_5 to check if any row has Zones_sup_dem as Demand
            #     logging.info("Inside look_for_trade_signals Zones Affected for 5 minute")
            #     for idx, row in Zones_affected_5.iterrows():
            #         if row['Zones_sup_dem'] == 'Demand':
            #             hit_the_Demand_zone = True
            #             logging.info(f"Demand row affected Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")
            # if hit_the_Demand_zone == False:
            Zone_over_Zone_1min = False
            if (Zone_on_5min_zone == False and 
                Zone_on_1min_zone == False and 
                not entry_reason.startswith("STRONG")):
                Zone_over_Zone_1min = check_zone_over_zone(df_1, "PUT", latest_1min['latest_zone'], latest_1min['prior_to_latest_zone'], latest_1min['two_prior_to_latest_zone'])
                if Zone_over_Zone_1min == True:
                    entry_reason = "STRONG" + entry_reason

            hit_the_Demand_zone = False
            #Don't check if Zones affected when it is zone on zone or zone over zone
            if Zone_on_5min_zone == False and Zone_on_1min_zone == False and Zone_over_Zone_1min == False and not entry_reason.startswith("STRONG"):
                if Zones_affected_1 is not None and not Zones_affected_1.empty:
                    #loop through each row of Zones_affected_1 to check if any row has Zones_sup_dem as Demand
                    logging.info("Inside look_for_trade Zones Affected for 1 minute")
                    for idx, row in Zones_affected_1.iterrows():
                        if row['Zones_sup_dem'] == 'Demand':
                            hit_the_Demand_zone = True
                            logging.info(f"Demand row affected Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")

                if Zones_affected_5 is not None and not Zones_affected_5.empty:
                    #loop through each row of Zones_affected_5 to check if any row has Zones_sup_dem as Demand
                    logging.info("Inside look_for_trade Zones Affected for 5 minute")
                    for idx, row in Zones_affected_5.iterrows():
                        if row['Zones_sup_dem'] == 'Demand':
                            hit_the_Demand_zone = True
                            logging.info(f"Demand row affected Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")
            
            if hit_the_Demand_zone == False:
                logging.info("PUT signal identified. - 1 ")
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$INTIMATION$Get ready for {instrument_name} PUT option trade."
                send_msg_to_telegram(message_to_send)
                trade_info['entry_reason'] = entry_reason
                trade_info['last_trade_crossover_timestamp'] = latest_1min['latest_crossover']['timestamp']                
                return "PUT", 'True', trade_info
            

    logging.info(f"Exiting function: look_for_trade_signals (no signal found for {instrument_name})")
    return "None", 'False', trade_info


def get_info_for_trade(master_dataframes, option_type_to_trade):
    """
    Finds the trading symbol and latest candle for the specified option type.
    """
    logging.info(f"Entering function: Get_info_for_trade for {option_type_to_trade}")
    for symbol in master_dataframes:
        if symbol.split()[-1] == option_type_to_trade:
            logging.info(f"Found matching symbol: {symbol}")
            latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1]
            return symbol, latest_1min_option_candle
    logging.info("No matching symbol found.")
    return None, None


def take_the_trade(symbol, option_1min_ready_for_trade_candle, master_dataframes, Zones_affected_1=None, Zones_affected_5=None, df_1=None):
    """
    Determines if a trade should be taken based on the latest option data.
    """
    logging.info(f"Entering function: take_the_trade for {symbol}")
    latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1]
    latest_option_1min = find_latest_signals(master_dataframes[symbol]['df_opt_1min'])
    option_type = symbol.split()[-1]

    last_stored_1min_data = df_1.iloc[-1]
    latest_1min = find_latest_signals(df_1)

    if latest_option_1min['latest_crossover']['crossover_signal'] == 'downward': 
        logging.info(f"The option crossover signal is downward. Invalidating trade.")
        return latest_1min['latest_crossover']['timestamp'], "False"

    if (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) >= datetime.timedelta(minutes=5):
        logging.info(f"Time difference between last stored and latest crossover is more than 5 minutes. Invalidating trade.")
        return latest_1min['latest_crossover']['timestamp'], "False"
    
    if option_type == "PUT":
        if (latest_1min_option_candle['crossover_signal'] == 'downward' and
            latest_1min_option_candle['timestamp'] > option_1min_ready_for_trade_candle['timestamp']):
            logging.info(f"PUT Trade invalidated. Signal reversed.")
            return latest_1min['latest_crossover']['timestamp'], "False"
        
        hit_the_Demand_zone = False
        if Zones_affected_1 is not None and not Zones_affected_1.empty:
            #loop through each row of Zones_affected_1 to check if any row has Zones_sup_dem as Demand
            logging.info("Inside take_the_trade Zones Affected for 1 minute")
            for idx, row in Zones_affected_1.iterrows():
                if row['Zones_sup_dem'] == 'Demand':
                    hit_the_Demand_zone = True
                    logging.info(f"Demand row affected Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")
                    return latest_1min['latest_crossover']['timestamp'], "False"

        if Zones_affected_5 is not None and not Zones_affected_5.empty:
            #loop through each row of Zones_affected_5 to check if any row has Zones_sup_dem as Demand
            logging.info("Inside take_the_trade Zones Affected for 5 minute")
            for idx, row in Zones_affected_5.iterrows():
                if row['Zones_sup_dem'] == 'Demand':
                    hit_the_Demand_zone = True
                    logging.info(f"Demand row affected Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")
                    return latest_1min['latest_crossover']['timestamp'], "False"
    
        if hit_the_Demand_zone == False and latest_1min_option_candle['low'] <= latest_1min_option_candle['WMA5']:
            logging.info(f"PUT Trade Taken at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['close']}")
            return latest_1min_option_candle['timestamp'], "Trade Taken"
        
    elif option_type == "CALL":
        if (latest_1min_option_candle['crossover_signal'] == 'downward' and
            latest_1min_option_candle['timestamp'] > option_1min_ready_for_trade_candle['timestamp']):
            logging.info(f"CALL Trade invalidated. Signal reversed.")
            return latest_1min['latest_crossover']['timestamp'], "False"
        
        hit_the_Supply_zone = False
        if Zones_affected_1 is not None and not Zones_affected_1.empty:
            #loop through each row of Zones_affected_1 to check if any row has Zones_sup_dem as Supply
            logging.info("Inside take_the_trade Zones Affected for 1 minute")
            for idx, row in Zones_affected_1.iterrows():
                if row['Zones_sup_dem'] == 'Supply':
                    hit_the_Supply_zone = True
                    logging.info(f"Supply row affected Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")
                    return latest_1min['latest_crossover']['timestamp'], "False"

        if Zones_affected_5 is not None and not Zones_affected_5.empty:
            #loop through each row of Zones_affected_5 to check if any row has Zones_sup_dem as Supply
            logging.info("Inside take_the_trade Zones Affected for 5 minute")
            for idx, row in Zones_affected_5.iterrows():
                if row['Zones_sup_dem'] == 'Supply':
                    hit_the_Supply_zone = True
                    logging.info(f"Supply row affected Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")                
                    return latest_1min['latest_crossover']['timestamp'], "False"
    
        if hit_the_Supply_zone == False and latest_1min_option_candle['low'] <= latest_1min_option_candle['WMA5']:
            logging.info(f"CALL Trade Taken at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['close']}")
            return latest_1min_option_candle['timestamp'], "Trade Taken"
        
    else:
        logging.info(f"No trade taken. Invalid symbol type {symbol}")
        return latest_1min['latest_crossover']['timestamp'], "False"
    
    logging.info("Exiting function: take_the_trade (conditions not met)")
    return None, "Take trade"

# def check_for_trade_exit(df_1, df_5, trade_symbol, time_trade_taken, 
#                          master_dataframes, trade_log_df, trade_resistant_df, opt_1min_latest_Demand, 
#                          opt_1min_latest_Supply, opt_5min_latest_Demand, opt_5min_latest_Supply, stop_loss, entry_reason, Zones_affected_1, Zones_affected_5):
def check_for_trade_exit(df_1, df_5, trade_info=pd.DataFrame(), trade_log_df=pd.DataFrame(), trade_resistant_df=pd.DataFrame(), 
						Zones_affected_1=pd.DataFrame(), Zones_affected_5=pd.DataFrame(), master_dataframes=None):
    """
    Checks for conditions to exit an ongoing trade.
    """
    trade_symbol = trade_info['symbol']
    time_trade_taken = trade_info['entry_time']
    opt_1min_latest_Demand = trade_info['opt_1min_latest_Demand']
    opt_1min_latest_Supply = trade_info['opt_1min_latest_Supply']
    opt_5min_latest_Demand = trade_info['opt_5min_latest_Demand']
    opt_5min_latest_Supply = trade_info['opt_5min_latest_Supply']
    stop_loss = trade_info['stop_loss']
    entry_reason = trade_info['entry_reason']

    logging.info(f"Entering function: check_for_trade_exit for {trade_symbol}")
    latest_1min_option_candle = master_dataframes[trade_symbol]['df_opt_1min'].iloc[-1]
    latest_5min_option_candle = master_dataframes[trade_symbol]['df_opt_5min'].iloc[-1]
    Option_1min_crossover_and_zone = find_latest_signals(master_dataframes[trade_symbol]['df_opt_1min'])
    result_1min = find_latest_signals(df_1)
    result_5min = find_latest_signals(df_5)
    option_type = trade_symbol.split()[-1]
    #lot_size = trade_info['lot_size'].astype(int)
    lot_size = trade_info['lot_size']
    #delta_val = (100/lot_size).round(2)
    delta_val = round((100/lot_size), 2)  # Fix: Use round() as a function instead of a method
    entry_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'Trade_entry_price'].iloc[0]
    current_opt_price = latest_1min_option_candle['close']
    
    logging.info(f" in profit flag: {trade_info['in_profit']}")
    if trade_info['in_profit'] == "False":
        if (latest_1min_option_candle['close'] > (entry_price + 3)):  #To be in profit
            logging.info(f"Minimum Profit achieved for {trade_symbol} at price: {latest_1min_option_candle['close']}")
            trade_info['in_profit'] = "True"
        elif (entry_price - latest_1min_option_candle['close']) > delta_val:
            logging.info(f"Never went to profit so reducing the loss")
            if ((entry_price - delta_val) > latest_1min_option_candle['close']):
                logging.info(f"Exiting the trade as price {latest_1min_option_candle['close']} is below entry price - 2")
                stop_loss = latest_1min_option_candle['close']
                trade_info['stop_loss'] = stop_loss.round(2)
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['close']}${latest_1min_option_candle['timestamp']}$stop_loss_exit${stop_loss}"
                send_msg_to_telegram(message_to_send)
                trade_info['exit_reason'] = 'stop_loss_exit'
                return 'Trade Exit', trade_info
            else:
                stop_loss = (entry_price - delta_val).round(2)
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                send_msg_to_telegram(message_to_send)
        
    if trade_info['in_profit'] == "True":
        if (latest_1min_option_candle['close'] - entry_price) < delta_val:
            logging.info(f"Profit was achieved but now going down, so securing minimum profit for {trade_symbol} at price: {latest_1min_option_candle['close']}")
            if((entry_price + delta_val) > latest_1min_option_candle['close']):
                logging.info(f"Exiting the trade as price {latest_1min_option_candle['close']} is below entry price + 2")
                stop_loss = latest_1min_option_candle['close']
                trade_info['stop_loss'] = stop_loss.round(2)
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['close']}${latest_1min_option_candle['timestamp']}$stop_loss_exit${stop_loss}"
                send_msg_to_telegram(message_to_send)
                trade_info['exit_reason'] = 'stop_loss_exit'
                return 'Trade Exit', trade_info
            else:
                stop_loss = entry_price + delta_val
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                send_msg_to_telegram(message_to_send)
                
    if (#latest_5min_option_candle['WMA10'] > stop_loss + 10 or  #To be in profit
        #latest_1min_option_candle['close'] > entry_price + 8): #To keep trailing stop loss
        latest_1min_option_candle['close'] > (entry_price + (4 * delta_val))): #To keep trailing stop loss
        if stop_loss < entry_price:
            logging.info(f"Adjusting stop loss to secure profit 1: from {stop_loss} to {entry_price + delta_val}")
            stop_loss = (entry_price + delta_val).round(2)
            # Message to be sent for telegram bot   
            message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
            send_msg_to_telegram(message_to_send)
            
    if (#latest_5min_option_candle['WMA10'] > stop_loss + 10 or  #To be in profit
        latest_1min_option_candle['close'] > (stop_loss + (4 * delta_val))): #To keep trailing stop loss
        if stop_loss > entry_price:
            logging.info(f"Adjusting stop loss to secure profit 2: from {stop_loss} to {stop_loss + delta_val}")
            stop_loss = (stop_loss + delta_val).round(2)
            # Message to be sent for telegram bot   
            message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
            send_msg_to_telegram(message_to_send)
            
    if (stop_loss > opt_1min_latest_Demand['low']): #To keep trailing stop loss
        logging.info(f"stop_loss: {stop_loss} and opt_1min_latest_Demand['low']: {opt_1min_latest_Demand['low']}")
        if stop_loss < latest_1min_option_candle['WMA45']:
            logging.info(f"Latest option 1 min demand at: {opt_1min_latest_Demand['timestamp']} and low value: {opt_1min_latest_Demand['low']}")
            logging.info(f"Adjusting stop loss 1: from {stop_loss} to {latest_1min_option_candle['WMA45']}")
            stop_loss = latest_1min_option_candle['WMA45']
            # Message to be sent for telegram bot   
            message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
            send_msg_to_telegram(message_to_send)
            
    # if (option_type == "CALL" and result_5min['prior_to_latest_zone']['Zones_sup_dem'] == 'Supply' and result_5min['prior_to_latest_zone']['Zone_Status'] == 'Valid'):
    #     if (df_1.iloc[-1]['high'] >= result_5min['prior_to_latest_zone']['low']):
    if (option_type == "CALL" and result_5min['prior_to_latest_zone']['Zones_sup_dem'] == 'Supply' and 
        (result_5min['prior_to_latest_zone']['Zone_Status'] == 'Valid') or (result_5min['prior_to_latest_zone']['Zone_Status'] == 'Tested')):
        if (df_1.iloc[-1]['high'] >= result_5min['prior_to_latest_zone']['low'] and 
            df_1.iloc[-1]['high'] < result_5min['prior_to_latest_zone']['high']):
            logging.info(f"timestamp of prior to latest zone SUPPLY: {result_5min['prior_to_latest_zone']['timestamp']} and status is: {result_5min['prior_to_latest_zone']['Zone_Status']}")
            logging.info(f"Latest index 1 min candle high: {df_1.iloc[-1]['high']} and zone is {result_5min['prior_to_latest_zone']['Zones_sup_dem']} with low value of : {result_5min['prior_to_latest_zone']['low']}")
            if trade_info["entry_reason"].startswith("STRONG"):
                if stop_loss < latest_1min_option_candle['WMA45']:
                    logging.info(f"Adjusting stop loss 2a: from {stop_loss} to {latest_1min_option_candle['WMA45']}")
                    logging.info(f"Latest index 1 min candle high: {df_1.iloc[-1]['high']} and previous {result_5min['prior_to_latest_zone']['Zones_sup_dem']} zone low value is: {result_5min['prior_to_latest_zone']['low']}")
                    stop_loss = latest_1min_option_candle['WMA45']
                    # Message to be sent for telegram bot   
                    message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                    send_msg_to_telegram(message_to_send)
            else:
                if stop_loss < latest_1min_option_candle['WMA26']:  
                    logging.info(f"Adjusting stop loss 2b: from {stop_loss} to {latest_1min_option_candle['WMA26']}")
                    logging.info(f"Latest index 1 min candle high: {df_1.iloc[-1]['high']} and previous {result_5min['prior_to_latest_zone']['Zones_sup_dem']} zone low value is: {result_5min['prior_to_latest_zone']['low']}")
                    stop_loss = latest_1min_option_candle['WMA26']
                    # Message to be sent for telegram bot   
                    message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                    send_msg_to_telegram(message_to_send)
                    
    # elif (option_type == "PUT" and result_5min['prior_to_latest_zone']['Zones_sup_dem'] == 'Demand' and result_5min['prior_to_latest_zone']['Zone_Status'] == 'Valid'):
    #       if (df_1.iloc[-1]['low'] <= result_5min['prior_to_latest_zone']['high']):
    elif (option_type == "PUT" and result_5min['prior_to_latest_zone']['Zones_sup_dem'] == 'Demand' and 
        (result_5min['prior_to_latest_zone']['Zone_Status'] == 'Valid') or (result_5min['prior_to_latest_zone']['Zone_Status'] == 'Tested')):
        if (df_1.iloc[-1]['low'] <= result_5min['prior_to_latest_zone']['high'] and 
            df_1.iloc[-1]['low'] > result_5min['prior_to_latest_zone']['low']):
            logging.info(f"timestamp of prior to latest zone DEMAND: {result_5min['prior_to_latest_zone']['timestamp']} and status is: {result_5min['prior_to_latest_zone']['Zone_Status']}")
            logging.info(f"Latest index 1 min candle low: {df_1.iloc[-1]['low']} and zone is {result_5min['prior_to_latest_zone']['Zones_sup_dem']} with high value of : {result_5min['prior_to_latest_zone']['high']}")
            if trade_info["entry_reason"].startswith("STRONG"):
                if stop_loss < latest_1min_option_candle['WMA45']:
                    logging.info(f"Latest index 1 min candle high: {df_1.iloc[-1]['low']} and previous {result_5min['prior_to_latest_zone']['Zones_sup_dem']} zone high value is: {result_5min['prior_to_latest_zone']['high']}")
                    logging.info(f"Adjusting stop loss 3a: from {stop_loss} to {latest_1min_option_candle['WMA45']}")
                    stop_loss = latest_1min_option_candle['WMA45']
                    # Message to be sent for telegram bot   
                    message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                    send_msg_to_telegram(message_to_send)
            else:
                if stop_loss < latest_1min_option_candle['WMA26']:  
                    logging.info(f"Latest index 1 min candle high: {df_1.iloc[-1]['low']} and previous {result_5min['prior_to_latest_zone']['Zones_sup_dem']} zone high value is: {result_5min['prior_to_latest_zone']['high']}")
                    logging.info(f"Adjusting stop loss 3b: from {stop_loss} to {latest_1min_option_candle['WMA26']}")
                    stop_loss = latest_1min_option_candle['WMA26']
                    # Message to be sent for telegram bot   
                    message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                    send_msg_to_telegram(message_to_send)
                    
    # When price hits opposite resistant zone, get ready for exit by adjusting stoploss

    hit_the_Demand_zone = False
    hit_the_Supply_zone = False
    if (option_type == "PUT"):
        if Zones_affected_1 is not None and not Zones_affected_1.empty:
            #loop through each row of Zones_affected_1 to check if any row has Zones_sup_dem as Demand
            logging.info("Inside check_for_exit Zones Affected for 1 minute")
            for idx, row in Zones_affected_1.iterrows():
                if row['Zones_sup_dem'] == 'Demand':
                    hit_the_Demand_zone = True
                    logging.info(f"Demand row affected Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")

        if Zones_affected_5 is not None and not Zones_affected_5.empty:
            #loop through each row of Zones_affected_5 to check if any row has Zones_sup_dem as Demand
            logging.info("Inside check_for_exit Zones Affected for 5 minute")
            for idx, row in Zones_affected_5.iterrows():
                if row['Zones_sup_dem'] == 'Demand':
                    hit_the_Demand_zone = True
                    logging.info(f"Demand row affected Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")
    else:
        if Zones_affected_1 is not None and not Zones_affected_1.empty:
            #loop through each row of Zones_affected_1 to check if any row has Zones_sup_dem as Supply
            logging.info("Inside check_for_exit Zones Affected for 1 minute")
            for idx, row in Zones_affected_1.iterrows():
                if row['Zones_sup_dem'] == 'Supply':
                    hit_the_Supply_zone = True
                    logging.info(f"Supply row affected Zones_affected_1: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")

        if Zones_affected_5 is not None and not Zones_affected_5.empty:
            #loop through each row of Zones_affected_5 to check if any row has Zones_sup_dem as Supply
            logging.info("Inside check_for_exit Zones Affected for 5 minute")
            for idx, row in Zones_affected_5.iterrows():
                if row['Zones_sup_dem'] == 'Supply':
                    hit_the_Supply_zone = True
                    logging.info(f"Supply row affected Zones_affected_5: {row['timestamp']} Zone_sup_dem: {row['Zones_sup_dem']}, Zone_Status: {row['Zone_Status']}")                

    if (option_type == "PUT" and hit_the_Demand_zone):
        logging.info(f"In PUT trade, the candle hit a Demand zone")
        if trade_info["entry_reason"].startswith("STRONG"):
            if stop_loss < latest_1min_option_candle['WMA45']:
                stop_loss = latest_1min_option_candle['WMA45']
                logging.info(f"Updated stop loss due to hit the Demand zone 1: {trade_symbol} and stop loss is: {stop_loss}")
                # Message to be sent for telegram bot   
                message_to_send =f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                send_msg_to_telegram(message_to_send)
        else:
            if stop_loss < latest_1min_option_candle['WMA26']:  
                stop_loss = latest_1min_option_candle['WMA26']
                logging.info(f"Updated stop loss due to hit the Demand zone 2: {trade_symbol} and stop loss is: {stop_loss}")
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                send_msg_to_telegram(message_to_send)

    if (option_type == "CALL" and hit_the_Supply_zone):
        logging.info(f"In CALL trade, the candle hit a Supply zone")
        if trade_info["entry_reason"].startswith("STRONG"):
            if stop_loss < latest_1min_option_candle['WMA45']:
                stop_loss = latest_1min_option_candle['WMA45']
                logging.info(f"Updated stop loss due to hit the Supply zone 3: {trade_symbol} and stop loss is: {stop_loss}")
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                send_msg_to_telegram(message_to_send)
        else:
            if stop_loss < latest_1min_option_candle['WMA26']:  
                stop_loss = latest_1min_option_candle['WMA26']
                logging.info(f"Updated stop loss due to hit the Supply zone 4: {trade_symbol} and stop loss is: {stop_loss}")
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                send_msg_to_telegram(message_to_send)

        
    # if not trade_resistant_df.empty:
    #     if (option_type == "PUT" and 
    #         trade_resistant_df is not None and
    #         not trade_resistant_df.empty and
    #         ((trade_resistant_df['Resistant_Zone'] == 'Demand') &
    #         (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
    #         ):
    #         num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
    #         num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
    #         if num_demand > num_supply:
    #             if stop_loss < latest_5min_option_candle['WMA5']:
    #                 stop_loss = latest_5min_option_candle['WMA5']
    #                 nearest_demand = trade_resistant_df[trade_resistant_df['Resistant_Zone'] == 'Demand'].iloc[0]
    #                 logging.info(f"Found 5 minute demand zone at {nearest_demand['low']} - {nearest_demand['high']}")
    #                 #logging.info(f"and zone timestamp: {nearest_demand['timestamp']}")
    #     elif (option_type == "CALL" and 
    #         trade_resistant_df is not None and
    #         not trade_resistant_df.empty and
    #         ((trade_resistant_df['Resistant_Zone'] == 'Supply') &
    #         (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
    #         ):
    #         num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
    #         num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
    #         if num_supply > num_demand:
    #             if stop_loss < latest_5min_option_candle['WMA5']:
    #                 stop_loss = latest_5min_option_candle['WMA5']
    #                 nearest_supply = trade_resistant_df[trade_resistant_df['Resistant_Zone'] == 'Supply'].iloc[0]
    #                 logging.info(f"Found 5 minute supply zone at {nearest_supply['low']} - {nearest_supply['high']}")
    #                 #logging.info(f"and zone timestamp: {nearest_supply['timestamp']}")
    
    # Time-based exit
    # if ((latest_1min_option_candle['timestamp'] - time_trade_taken) > datetime.timedelta(minutes=15) and 
    #    (latest_1min_option_candle['low'] <= (trade_log_recent_entry['Trade_entry_price']))):
    #     logging.info(f"Exit due to time limit: {trade_symbol}")
    #     # Alert to Mobile " Exit trade_symbol (extract from symbol puts or calls) for Nifty 50"
    #     return 'Trade Exit', stop_loss
    

    # Signal reversal exit
    if option_type == "PUT":        
        if (result_1min['latest_crossover'] is not None and
            result_1min['latest_crossover']['crossover_signal'] == 'upward' and
            result_1min['latest_crossover']['timestamp'] > time_trade_taken):
            if (result_5min['prior_to_latest_zone']['Zones_sup_dem'] == 'Demand' and 
                ((result_5min['prior_to_latest_zone']['Zone_Status'] == 'Valid') or (result_5min['prior_to_latest_zone']['Zone_Status'] == 'Tested')) and
                df_1.iloc[-1]['low'] <= result_5min['prior_to_latest_zone']['high'] and 
                df_1.iloc[-1]['low'] > result_5min['prior_to_latest_zone']['low']):
                if stop_loss < latest_1min_option_candle['WMA26']:  
                    stop_loss = latest_1min_option_candle['WMA26']
                    logging.info(f"Updated stop loss due to opposite crossover 5: {trade_symbol} and stop loss is: {stop_loss}")
                    # Message to be sent for telegram bot   
                    message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                    send_msg_to_telegram(message_to_send)
            else:
                if trade_info["entry_reason"].startswith("STRONG"):
                    if stop_loss < latest_1min_option_candle['WMA45']:
                        stop_loss = latest_1min_option_candle['WMA45']
                        logging.info(f"Updated stop loss due to opposite crossover 1: {trade_symbol} and stop loss is: {stop_loss}")
                        # Message to be sent for telegram bot   
                        message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                        send_msg_to_telegram(message_to_send)
                else:
                    if stop_loss < latest_1min_option_candle['WMA26']:  
                        stop_loss = latest_1min_option_candle['WMA26']
                        logging.info(f"Updated stop loss due to opposite crossover 2: {trade_symbol} and stop loss is: {stop_loss}")
                        # Message to be sent for telegram bot   
                        message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                        send_msg_to_telegram(message_to_send)

                
                # if (Option_1min_crossover_and_zone['latest_zone']['crossover_signal'] == 'downward' and
                #     Option_1min_crossover_and_zone['latest_zone']['timestamp'] > time_trade_taken):
                #     logging.info(f"Exit PUT trade due to upward crossover at index and downward crossover at option. at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")    
                # else:
                #     logging.info(f"Exit PUT trade due to upward crossover. at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")
                # # Alert to Mobile " Exit trade_symbol (extract from symbol puts or calls) for Nifty 50"
                # # Message to be sent for telegram bot   
                # message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['low']}${latest_1min_option_candle['timestamp']}"
                # send_msg_to_telegram(message_to_send)
                # trade_info['exit_reason'] = 'opposite_crossover_exit'
                # return 'Trade Exit', trade_info
        # if Zones_affected_5 is not None and not Zones_affected_5.empty:
        #     logging.info(f"5 minute Zones affected during PUTS: {Zones_affected_5}")
        #     if (Zones_affected_5['Zones_sup_dem'] == 'Demand').any():
        #         logging.info(f"Found demand zone in 5 min")
        #         if stop_loss < latest_1min_option_candle['low']:
        #             stop_loss = latest_1min_option_candle['low']
        # elif Zones_affected_1 is not None and not Zones_affected_1.empty:
        #     logging.info(f"1 minute Zones affected during PUTS: {Zones_affected_1}")
        #     if (Zones_affected_1['Zones_sup_dem'] == 'Demand').any():
        #         logging.info(f"Found demand zone in 1 min")
        #         if stop_loss < latest_1min_option_candle['low']:
        #             stop_loss = latest_1min_option_candle['low']
    elif option_type == "CALL":
        if (result_1min['latest_crossover'] is not None and
            result_1min['latest_crossover']['crossover_signal'] == 'downward' and
            result_1min['latest_crossover']['timestamp'] > time_trade_taken):
            if (result_5min['prior_to_latest_zone']['Zones_sup_dem'] == 'Supply' and 
                ((result_5min['prior_to_latest_zone']['Zone_Status'] == 'Valid') or (result_5min['prior_to_latest_zone']['Zone_Status'] == 'Tested')) and
                df_1.iloc[-1]['high'] >= result_5min['prior_to_latest_zone']['low'] and 
                df_1.iloc[-1]['high'] < result_5min['prior_to_latest_zone']['high']):
                if stop_loss < latest_1min_option_candle['WMA26']:  
                    stop_loss = latest_1min_option_candle['WMA26']
                    logging.info(f"Updated stop loss due to opposite crossover 6: {trade_symbol} and stop loss is: {stop_loss}")
                    # Message to be sent for telegram bot   
                    message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                    send_msg_to_telegram(message_to_send)
            else:
                if trade_info["entry_reason"].startswith("STRONG"):
                    if stop_loss < latest_1min_option_candle['WMA45']:
                        stop_loss = latest_1min_option_candle['WMA45']
                        logging.info(f"Updated stop loss due to opposite crossover 3: {trade_symbol} and stop loss is: {stop_loss}")
                        # Message to be sent for telegram bot   
                        message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                        send_msg_to_telegram(message_to_send)
                else:
                    if stop_loss < latest_1min_option_candle['WMA26']:  
                        stop_loss = latest_1min_option_candle['WMA26']
                        logging.info(f"Updated stop loss due to opposite crossover 4: {trade_symbol} and stop loss is: {stop_loss}")
                        # Message to be sent for telegram bot   
                        message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol} current price: {current_opt_price} (Entry price: {entry_price})"
                        send_msg_to_telegram(message_to_send)
    
                # if (Option_1min_crossover_and_zone['latest_zone']['crossover_signal'] == 'downward' and
                #     Option_1min_crossover_and_zone['latest_zone']['timestamp'] > time_trade_taken):
                #     logging.info(f"Exit CALL trade due to downward crossover at index and downward crossover at option. at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")    
                # else:
                #     logging.info(f"Exit CALL trade due to downward crossover. at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")
                # # Alert to Mobile " Exit trade_symbol (extract from symbol puts or calls) for Nifty 50"
                # # Message to be sent for telegram bot   
                # message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['low']}${latest_1min_option_candle['timestamp']}"
                # send_msg_to_telegram(message_to_send)
                # trade_info['exit_reason'] = 'opposite_crossover_exit'
                # return 'Trade Exit', trade_info
        # if Zones_affected_5 is not None and not Zones_affected_5.empty:
        #     logging.info(f"5 minute Zones affected during CALLS: {Zones_affected_5}")
        #     if (Zones_affected_5['Zones_sup_dem'] == 'Supply').any():
        #         logging.info(f"Found supply zone in 5 min")
        #         if stop_loss < latest_1min_option_candle['low']:
        #             stop_loss = latest_1min_option_candle['low']
        # elif Zones_affected_1 is not None and not Zones_affected_1.empty:
        #     logging.info(f"1 minute Zones affected during CALLS: {Zones_affected_1}")
        #     if (Zones_affected_1['Zones_sup_dem'] == 'Supply').any():
        #         logging.info(f"Found supply zone in 1 min")
        #         if stop_loss < latest_1min_option_candle['low']:
        #             stop_loss = latest_1min_option_candle['low']
    
    logging.info(f"Stop loss: {stop_loss}")
    trade_info['stop_loss'] = stop_loss.round(2)
    
    # Profit target exit
    # if (latest_1min_option_candle['low'] > (trade_log_recent_entry['Trade_entry_price'] + 15)):
    #     logging.info(f"Exit for profit target: {trade_symbol}")
    #     # Alert to Mobile " Move stop loss to profit trade_symbol (extract from symbol puts or calls) for Nifty 50"
    #     return 'Trade Exit', stop_loss
    
    # Stop loss exit
    #if (latest_1min_option_candle['low'] <= (latest_5min_option_candle['WMA13'] - 0.10)):
    if (latest_1min_option_candle['low'] < stop_loss):
        logging.info(f"current_opt_price - Close : {current_opt_price} and latest_1min_option_candle['low']: {latest_1min_option_candle['low']}")
        logging.info(f"Exit for stop loss: {trade_symbol} and stop loss trigger is {stop_loss} at current price: {current_opt_price} (Entry price: {entry_price})")
        # Alert to Mobile " Exit trade_symbol (extract from symbol puts or calls) for Nifty 50"
        # Message to be sent for telegram bot   
        message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['close']}${latest_1min_option_candle['timestamp']}$stop_loss_exit${stop_loss}"
        send_msg_to_telegram(message_to_send)
        trade_info['exit_reason'] = 'stop_loss_exit'
        return 'Trade Exit', trade_info


    logging.info(f"Continue to hold trade: {trade_symbol} at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss}")
    # Message to be sent for telegram bot   
    entry_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'Trade_entry_price'].iloc[0]
    current_opt_price = latest_1min_option_candle['close']
    message_to_send = f"===Algo_Trading===$INTIMATION$Continue to hold trade for ${trade_symbol} at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})"
    send_msg_to_telegram(message_to_send)
    trade_info['exit_reason'] = 'Not_exiting_yet'
    return 'Trade Taken', trade_info

def exit_the_trade(trade_info, master_dataframes, trade_log_df, to_date_str):
    """
    Records the exit details of a trade and saves the log.
    """
    logging.info(f"Entering function: exit_the_trade for {trade_info['symbol']}")
    latest_1min_option_candle = master_dataframes[trade_info['symbol']]['df_opt_1min'].iloc[-1]
    logging.info(f"{trade_info['symbol']} Trade Exited at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['close']}")
    
    trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'trade_exit_time'] = latest_1min_option_candle['timestamp']
    if trade_info['exit_reason'] == 'stop_loss_exit':
        trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'Trade_exit_price'] = trade_info['stop_loss']
    else:
        trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'Trade_exit_price'] = latest_1min_option_candle['close']

    try:
        # user_df = pd.read_csv(config.OPTIONS_CSV_FILE)
        # options_lot_size_df = pd.read_csv(config.OPTIONS_LOT_SIZE_CSV)
        
        # if trade_info['symbol'].split()[0] == "NIFTY":
        #     lot_size = 75
        # elif trade_info['symbol'].split()[0] == "BANKNIFTY":
        #     lot_size = 30
        # else:
        #     lot_size = 65
        #lot_size = trade_info['lot_size'].astype(int)
        lot_size = trade_info['lot_size']

        logging.info(f"Lot size for {trade_info['symbol']} is {lot_size}")

        # Default lot size for NIFTY if not found
        # underlying_symbol = user_df[user_df['DISPLAY_NAME'] == symbol]['UNDERLYING_SYMBOL'].iloc[0]
        # if not options_lot_size_df[options_lot_size_df['SYMBOL'] == underlying_symbol].empty:
        #     lot_size = options_lot_size_df[options_lot_size_df['SYMBOL'] == underlying_symbol]['LOT_SIZE'].iloc[0]

        entry_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'Trade_entry_price'].iloc[0]
        if trade_info['exit_reason'] == 'stop_loss_exit':
            exit_price = trade_info['stop_loss']
        else:
            exit_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'Trade_exit_price'].iloc[0]
        
        pnl = (exit_price - entry_price) * lot_size - 60  # Assuming 60 for charges
        trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'profit_loss'] = pnl.round(2)
        logging.info(f"P&L for {trade_info['symbol']}: {pnl}")

    except (FileNotFoundError, IndexError) as e:
        logging.error(f"Could not calculate P&L for {trade_info['symbol']}: {e}")
        trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'profit_loss'] = 'Calculation Error'

    logging.info(f"Trade log updated: \n{trade_log_df}")
    # Message to be sent for telegram bot   
    last_rec_of_trade_log = trade_log_df[trade_log_df['trade_entry_time'] == trade_info['entry_time']]
    message_to_send = f"===Algo_Trading===$INTIMATION$Trade_log${last_rec_of_trade_log.to_string(index=False, justify='left')}"
    send_msg_to_telegram(message_to_send)

    # Save trade log
    os.makedirs(config.OUTPUT_DIR_TRADE_LOG, exist_ok=True)
    output_filename = f"trade_log_{to_date_str}.csv"
    output_path = os.path.join(config.OUTPUT_DIR_TRADE_LOG, output_filename)
    
    trade_log_to_save = trade_log_df[trade_log_df['trade_entry_time'] == trade_info['entry_time']]

    if os.path.exists(output_path):
        try:
            existing_df = pd.read_csv(output_path)
            combined_df = pd.concat([existing_df, trade_log_to_save], ignore_index=True)
            combined_df.drop_duplicates(subset=["Instrument_name", "trade_entry_time"], keep='last', inplace=True)
            combined_df.to_csv(output_path, index=False)
        except Exception as e:
            logging.error(f"Error appending to trade log: {e}")
    else:
        trade_log_to_save.to_csv(output_path, index=False)

    return True