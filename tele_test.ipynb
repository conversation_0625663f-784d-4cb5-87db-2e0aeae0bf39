{"cells": [{"cell_type": "code", "execution_count": 3, "id": "1c3c240e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chat ID: -4881702344 | Chat Title: ಆತ್ಮ ನಿರ್ಭರ | Message: /hello @AATHMA_NIRBHARA_bot\n"]}], "source": ["import asyncio\n", "from telegram import Bot\n", "\n", "TELEGRAM_BOT_TOKEN = '**********************************************'\n", "\n", "async def main():\n", "    # Create bot object\n", "    bot = Bot(token=TELEGRAM_BOT_TOKEN)\n", "\n", "    # Get updates\n", "    updates = await bot.get_updates()\n", "\n", "    if not updates:\n", "        print(\"No updates found\")\n", "    else:\n", "        for update in updates:\n", "            if update.message:\n", "                # Show all message data if needed\n", "                # print(update.message)\n", "\n", "                # Show only chat id, title and message\n", "                chat_id = update.message.chat.id\n", "                chat_title = update.message.chat.title\n", "                message_text = update.message.text\n", "                print(f\"Chat ID: {chat_id} | Chat Title: {chat_title} | Message: {message_text}\")\n", "\n", "# Corrected line:\n", "await main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e4dcd8fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Message sent successfully!\n"]}], "source": ["from telegram import Bot\n", "import asyncio\n", "\n", "# It's good practice to not hardcode tokens.\n", "# Consider using environment variables or a config file for security.\n", "TELEGRAM_BOT_TOKEN = '**********************************************'\n", "CHAT_ID = '-4881702344'\n", "\n", "async def main():\n", "    \"\"\"\n", "    Initializes the bot and sends the message.\n", "    This is the main entry point for the asynchronous logic.\n", "    \"\"\"\n", "    bot = Bot(token=TELEGRAM_BOT_TOKEN)\n", "    \n", "    # The `async with` context manager handles bot.initialize() and bot.shutdown()\n", "    # for all operations within this block. This is more efficient than\n", "    # initializing/shutting down for every single message.\n", "    async with bot:\n", "        # Test messages\n", "        messages = [\n", "            \"\"\"\n", "            🙏 ಆತ್ಮನಿರ್ಭರ ಗುಂಪಿಗೆ ಎಲ್ಲರಿಗೂ ಹೃತ್ಪೂರ್ವಕ ಸ್ವಾಗತ!\n", "\n", "            🌟 ಇಲ್ಲಿ ನಾವು ಜ್ಞಾನವನ್ನು ಹಂಚಿಕೊಳ್ಳುತ್ತೇವೆ, ಪರಸ್ಪರ ಬೆಳೆದುಕೊಳ್ಳುತ್ತೇವೆ ಮತ್ತು ಯಶಸ್ಸಿನತ್ತ ಹೆಜ್ಜೆ ಇಡುತ್ತೇವೆ.\n", "\n", "            📢 ದಯವಿಟ್ಟು ಗುಂಪಿನ ನಿಯಮಗಳನ್ನು ಪಾಲಿಸಿ ಮತ್ತು ಸಕ್ರಿಯವಾಗಿ ಭಾಗವಹಿಸಿ.\n", "\n", "            💬 ನಿಮ್ಮ ಸಲಹೆ, ಅನುಭವ ಅಥವಾ ಪ್ರಶ್ನೆಗಳನ್ನು ಹಂಚಿಕೊಳ್ಳಿ – ಪ್ರತಿಯೊಬ್ಬ ಸದಸ್ಯನ ಅಭಿಪ್ರಾಯವು ಮುಖ್ಯವಾಗಿದೆ!\n", "\n", "            🚀 ಬನ್ನಿ, ಈ ಪ್ರಯಾಣವನ್ನು ಒಟ್ಟಾಗಿ ಸ್ಫೂರ್ತಿದಾಯಕವಾಗಿ ರೂಪಿಸೋಣ!\n", "\n", "            🙌 ನಿಮ್ಮ ಬೆಂಬಲ ಮತ್ತು ಸಕ್ರಿಯ ಭಾಗವಹಿಸುವಿಕೆಗೆ ಧನ್ಯವಾದಗಳು!\n", "            \"\"\"\n", "        ]\n", "\n", "        if messages:\n", "            text = '\\n'.join(messages)\n", "            await bot.send_message(text=text, chat_id=CHAT_ID)\n", "            print(\"Message sent successfully!\")\n", "\n", "# In a Jupyter/IPython environment, you can `await` a top-level coroutine.\n", "# This runs the coroutine on the existing event loop, fixing the error.\n", "await main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "beb4fc61", "metadata": {}, "outputs": [], "source": ["from telegram import Bot\n", "import asyncio\n", "\n", "# It's good practice to not hardcode tokens.\n", "# Consider using environment variables or a config file for security.\n", "TELEGRAM_BOT_TOKEN = '**********************************************'\n", "\n", "# Define a default chat ID if you frequently send messages to the same chat.\n", "# You can override this by passing a different chat_id to the function.\n", "DEFAULT_CHAT_ID = '-4881702344'\n", "\n", "async def send_telegram_message(message_text: str, target_chat_id: str = DEFAULT_CHAT_ID):\n", "    \"\"\"\n", "    Sends a text message to a specified Telegram chat.\n", "\n", "    This function initializes a Telegram Bot object, uses an async context manager\n", "    to ensure proper connection handling, and sends the provided message text\n", "    to the target chat ID.\n", "\n", "    Args:\n", "        message_text (str): The text content of the message to send.\n", "        target_chat_id (str): The ID of the chat where the message will be sent.\n", "                              Defaults to DEFAULT_CHAT_ID.\n", "    \"\"\"\n", "    bot = Bot(token=TELEGRAM_BOT_TOKEN)\n", "    \n", "    # The `async with bot:` context manager handles bot.initialize() and bot.shutdown()\n", "    # for all operations within this block. This is more efficient than\n", "    # initializing/shutting down the bot for every single message.\n", "    async with bot:\n", "        try:\n", "            await bot.send_message(chat_id=target_chat_id, text=message_text)\n", "            print(f\"Message successfully sent to chat ID: {target_chat_id}\")\n", "        except Exception as e:\n", "            print(f\"Error sending message to chat ID {target_chat_id}: {e}\")\n", "\n", "# --- How to use this function in your Jupyter/IPython environment ---\n", "\n", "# You can now call `send_telegram_message` directly using `await` in any cell.\n", "\n", "# Example 1: Sending the original test message to the default chat ID\n", "# (You would paste this into a new cell and run it)\n", "#\n", "# test_messages_content = \"\"\"\n", "# 🙏 ಆತ್ಮನಿರ್ಭರ ಗುಂಪಿಗೆ ಎಲ್ಲರಿಗೂ ಹೃತ್ಪೂರ್ವಕ ಸ್ವಾಗತ!\n", "#\n", "# 🌟 ಇಲ್ಲಿ ನಾವು ಜ್ಞಾನವನ್ನು ಹಂಚಿಕೊಳ್ಳುತ್ತೇವೆ, ಪರಸ್ಪರ ಬೆಳೆದುಕೊಳ್ಳುತ್ತೇವೆ ಮತ್ತು ಯಶಸ್ಸಿನತ್ತ ಹೆಜ್ಜೆ ಇಡುತ್ತೇವೆ.\n", "#\n", "# 📢 ದಯವಿಟ್ಟು ಗುಂಪಿನ ನಿಯಮಗಳನ್ನು ಪಾಲಿಸಿ ಮತ್ತು ಸಕ್ರಿಯವಾಗಿ ಭಾಗವಹಿಸಿ.\n", "#\n", "# 💬 ನಿಮ್ಮ ಸಲಹೆ, ಅನುಭವ ಅಥವಾ ಪ್ರಶ್ನೆಗಳನ್ನು ಹಂಚಿಕೊಳ್ಳಿ – ಪ್ರತಿಯೊಬ್ಬ ಸದಸ್ಯನ ಅಭಿಪ್ರಾಯವು ಮುಖ್ಯವಾಗಿದೆ!\n", "#\n", "# 🚀 ಬನ್ನಿ, ಈ ಪ್ರಯಾಣವನ್ನು ಒಟ್ಟಾಗಿ ಸ್ಫೂರ್ತಿದಾಯಕವಾಗಿ ರೂಪಿಸೋಣ!\n", "# \"\"\"\n", "#\n", "# await send_telegram_message(test_messages_content)\n", "\n", "# Example 2: Sending a simple message to the default chat ID\n", "# (You would paste this into a new cell and run it)\n", "#\n", "# await send_telegram_message(\"Hello from my reusable Telegram sender function!\")\n", "\n", "# Example 3: Sending a message to a different chat ID (if you have one)\n", "# (You would paste this into a new cell and run it)\n", "#\n", "# # Replace 'YOUR_OTHER_CHAT_ID' with the actual ID of another chat\n", "# await send_telegram_message(\"This message goes to a different group.\", \"YOUR_OTHER_CHAT_ID\")\n", "\n", "# --- Important Note ---\n", "# The original `async def main():` function and the `await main()` call are no longer needed\n", "# as `send_telegram_message` is now your reusable function.\n", "# You should remove them from your notebook cell.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "84d8fb0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["********************************** \n", "Fetching data for 13 at interval 1\n", "*******************************************\n", "\n", "                  timestamp      open      high           low     close  \\\n", "0 2025-06-19 09:16:00+05:30  24785.85  24813.95  24785.850000  24809.85   \n", "1 2025-06-19 09:17:00+05:30  24810.70  24810.70  24784.200000  24796.75   \n", "2 2025-06-19 09:18:00+05:30  24797.70  24797.70  24759.250000  24759.30   \n", "3 2025-06-19 09:19:00+05:30  24754.70  24759.55  24751.550781  24756.20   \n", "4 2025-06-19 09:20:00+05:30  24755.30  24755.65  24738.099609  24747.70   \n", "5 2025-06-19 09:21:00+05:30  24747.50  24779.00  24746.450000  24779.00   \n", "6 2025-06-19 09:22:00+05:30  24778.95  24795.85  24778.950000  24794.45   \n", "7 2025-06-19 09:23:00+05:30  24793.95  24796.05  24782.450000  24792.65   \n", "8 2025-06-19 09:24:00+05:30  24792.55  24810.55  24790.000000  24809.75   \n", "9 2025-06-19 09:25:00+05:30  24809.40  24825.25  24808.950000  24814.15   \n", "\n", "   volume  \n", "0     0.0  \n", "1     0.0  \n", "2     0.0  \n", "3     0.0  \n", "4     0.0  \n", "5     0.0  \n", "6     0.0  \n", "7     0.0  \n", "8     0.0  \n", "9     0.0  \n", "                     timestamp      open      high       low     close  volume\n", "2614 2025-06-27 15:20:00+05:30  25646.10  25649.95  25643.85  25645.00     0.0\n", "2615 2025-06-27 15:21:00+05:30  25645.35  25646.15  25641.40  25642.05     0.0\n", "2616 2025-06-27 15:22:00+05:30  25642.80  25642.90  25639.05  25641.50     0.0\n", "2617 2025-06-27 15:23:00+05:30  25642.75  25644.30  25638.80  25641.25     0.0\n", "2618 2025-06-27 15:24:00+05:30  25642.60  25643.85  25638.90  25642.30     0.0\n", "2619 2025-06-27 15:25:00+05:30  25641.25  25648.10  25639.35  25648.10     0.0\n", "2620 2025-06-27 15:26:00+05:30  25645.55  25645.65  25638.85  25640.70     0.0\n", "2621 2025-06-27 15:27:00+05:30  25638.95  25640.60  25634.60  25634.60     0.0\n", "2622 2025-06-27 15:28:00+05:30  25636.55  25639.40  25629.95  25635.30     0.0\n", "2623 2025-06-27 15:29:00+05:30  25634.00  25639.50  25629.60  25632.45     0.0\n"]}, {"ename": "OSError", "evalue": "Cannot save file into a non-existent directory: 'test_multi_intru'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>\u001b[39m                                   <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 36\u001b[39m\n\u001b[32m     34\u001b[39m     \u001b[38;5;28mprint\u001b[39m(df.head(\u001b[32m10\u001b[39m))\n\u001b[32m     35\u001b[39m     \u001b[38;5;28mprint\u001b[39m(df.tail(\u001b[32m10\u001b[39m))\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m     \u001b[43mdf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtest_multi_intru/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43minstrument\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43msecurity_id\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m_\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43minterval\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m_data.csv\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     38\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mNo valid DataFrame returned for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minstrument[\u001b[33m'\u001b[39m\u001b[33msecurity_id\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m at interval \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minterval\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/util/_decorators.py:333\u001b[39m, in \u001b[36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    327\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) > num_allow_args:\n\u001b[32m    328\u001b[39m     warnings.warn(\n\u001b[32m    329\u001b[39m         msg.format(arguments=_format_argument_list(allow_args)),\n\u001b[32m    330\u001b[39m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[32m    331\u001b[39m         stacklevel=find_stack_level(),\n\u001b[32m    332\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m333\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/core/generic.py:3986\u001b[39m, in \u001b[36mNDFrame.to_csv\u001b[39m\u001b[34m(self, path_or_buf, sep, na_rep, float_format, columns, header, index, index_label, mode, encoding, compression, quoting, quotechar, lineterminator, chunksize, date_format, doublequote, escapechar, decimal, errors, storage_options)\u001b[39m\n\u001b[32m   3975\u001b[39m df = \u001b[38;5;28mself\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(\u001b[38;5;28mself\u001b[39m, ABCDataFrame) \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m.to_frame()\n\u001b[32m   3977\u001b[39m formatter = DataFrameFormatter(\n\u001b[32m   3978\u001b[39m     frame=df,\n\u001b[32m   3979\u001b[39m     header=header,\n\u001b[32m   (...)\u001b[39m\u001b[32m   3983\u001b[39m     decimal=decimal,\n\u001b[32m   3984\u001b[39m )\n\u001b[32m-> \u001b[39m\u001b[32m3986\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mDataFrameRenderer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mformatter\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_csv\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   3987\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpath_or_buf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3988\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlineterminator\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlineterminator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3989\u001b[39m \u001b[43m    \u001b[49m\u001b[43msep\u001b[49m\u001b[43m=\u001b[49m\u001b[43msep\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3990\u001b[39m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3991\u001b[39m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3992\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3993\u001b[39m \u001b[43m    \u001b[49m\u001b[43mquoting\u001b[49m\u001b[43m=\u001b[49m\u001b[43mquoting\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3994\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3995\u001b[39m \u001b[43m    \u001b[49m\u001b[43mindex_label\u001b[49m\u001b[43m=\u001b[49m\u001b[43mindex_label\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3996\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3997\u001b[39m \u001b[43m    \u001b[49m\u001b[43mchunksize\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunksize\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3998\u001b[39m \u001b[43m    \u001b[49m\u001b[43mquotechar\u001b[49m\u001b[43m=\u001b[49m\u001b[43mquotechar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3999\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdate_format\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdate_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4000\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdoublequote\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdoublequote\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4001\u001b[39m \u001b[43m    \u001b[49m\u001b[43mescapechar\u001b[49m\u001b[43m=\u001b[49m\u001b[43mescapechar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4002\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4003\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/io/formats/format.py:1014\u001b[39m, in \u001b[36mDataFrameRenderer.to_csv\u001b[39m\u001b[34m(self, path_or_buf, encoding, sep, columns, index_label, mode, compression, quoting, quotechar, lineterminator, chunksize, date_format, doublequote, escapechar, errors, storage_options)\u001b[39m\n\u001b[32m    993\u001b[39m     created_buffer = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m    995\u001b[39m csv_formatter = CSVFormatter(\n\u001b[32m    996\u001b[39m     path_or_buf=path_or_buf,\n\u001b[32m    997\u001b[39m     lineterminator=lineterminator,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1012\u001b[39m     formatter=\u001b[38;5;28mself\u001b[39m.fmt,\n\u001b[32m   1013\u001b[39m )\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m \u001b[43mcsv_formatter\u001b[49m\u001b[43m.\u001b[49m\u001b[43msave\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m created_buffer:\n\u001b[32m   1017\u001b[39m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(path_or_buf, StringIO)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/io/formats/csvs.py:251\u001b[39m, in \u001b[36mCSVFormatter.save\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    247\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    248\u001b[39m \u001b[33;03mCreate the writer & save.\u001b[39;00m\n\u001b[32m    249\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    250\u001b[39m \u001b[38;5;66;03m# apply compression and byte/text conversion\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m251\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    253\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    254\u001b[39m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    255\u001b[39m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    256\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    257\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    258\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m handles:\n\u001b[32m    259\u001b[39m     \u001b[38;5;66;03m# Note: self.encoding is irrelevant here\u001b[39;00m\n\u001b[32m    260\u001b[39m     \u001b[38;5;28mself\u001b[39m.writer = csvlib.writer(\n\u001b[32m    261\u001b[39m         handles.handle,\n\u001b[32m    262\u001b[39m         lineterminator=\u001b[38;5;28mself\u001b[39m.lineterminator,\n\u001b[32m   (...)\u001b[39m\u001b[32m    267\u001b[39m         quotechar=\u001b[38;5;28mself\u001b[39m.quotechar,\n\u001b[32m    268\u001b[39m     )\n\u001b[32m    270\u001b[39m     \u001b[38;5;28mself\u001b[39m._save()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/io/common.py:749\u001b[39m, in \u001b[36mget_handle\u001b[39m\u001b[34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[39m\n\u001b[32m    747\u001b[39m \u001b[38;5;66;03m# Only for write methods\u001b[39;00m\n\u001b[32m    748\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode \u001b[38;5;129;01mand\u001b[39;00m is_path:\n\u001b[32m--> \u001b[39m\u001b[32m749\u001b[39m     \u001b[43mcheck_parent_directory\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    751\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m compression:\n\u001b[32m    752\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m compression != \u001b[33m\"\u001b[39m\u001b[33mzstd\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m    753\u001b[39m         \u001b[38;5;66;03m# compression libraries do not like an explicit text-mode\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/io/common.py:616\u001b[39m, in \u001b[36mcheck_parent_directory\u001b[39m\u001b[34m(path)\u001b[39m\n\u001b[32m    614\u001b[39m parent = Path(path).parent\n\u001b[32m    615\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m parent.is_dir():\n\u001b[32m--> \u001b[39m\u001b[32m616\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m(\u001b[33mrf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCannot save file into a non-existent directory: \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mparent\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mOSError\u001b[39m: Cannot save file into a non-existent directory: 'test_multi_intru'"]}], "source": ["# import requests\n", "# import time\n", "import pandas as pd\n", "import datetime\n", "import utils.data_handler as data_handler\n", "\n", "# API_URL = \"https://api.dhan.co/charts/historical\"\n", "# TOKEN = \"your_access_token\"\n", "# headers = {\n", "#     \"Authorization\": f\"Bearer {TOKEN}\",\n", "#     \"Content-Type\": \"application/json\"\n", "# }\n", "\n", "# NIFTY_SECURITY_ID = \"13\"\n", "# NIFTY_EXCHANGE_SEGMENT = \"IDX_I\"\n", "# NIFTY_INSTRUMENT_TYPE = \"INDEX\"\n", "\n", "instrument_list = [\n", "    {\"security_id\": \"13\", \"exchange_segment\": \"IDX_I\", \"instrument_type\": \"INDEX\"},    #NIFTY\n", "    {\"security_id\": \"25\", \"exchange_segment\": \"IDX_I\", \"instrument_type\": \"INDEX\"},    #BANKNIFTY\n", "    {\"security_id\": \"27\", \"exchange_segment\": \"IDX_I\", \"instrument_type\": \"INDEX\"}     #FINNIFTY\n", "    # {\"security_id\": \"INE467B01029\", \"exchange_segment\": \"NSE_EQ\"},\n", "    # Add more...\n", "]\n", "intervals_to_fetch = ['1', '5', '60']\n", "for instrument in instrument_list:\n", "    for interval in intervals_to_fetch:\n", "    \n", "        print(\"********************************** \")\n", "        print(f\"Fetching data for {instrument['security_id']} at interval {interval}\")\n", "        print(\"*******************************************\\n\")\n", "        df, from_date, to_date = data_handler.fetch_historical_data(security_id=instrument[\"security_id\"], exchange_segment=instrument[\"exchange_segment\"], instrument_type=instrument[\"instrument_type\"], interval=interval)\n", "        if isinstance(df, pd.DataFrame) and not df.empty:\n", "            print(df.head(10))\n", "            print(df.tail(10))\n", "            df.to_csv(f\"test_multi_intru/{instrument['security_id']}_{interval}_data.csv\", index=False)\n", "        else:\n", "            print(f\"No valid DataFrame returned for {instrument['security_id']} at interval {interval}\")\n", "        \n", "    print(f\"Data fetched from {from_date} to {to_date} for {instrument['security_id']} on {instrument['exchange_segment']}\")\n"]}, {"cell_type": "code", "execution_count": 1, "id": "cba78f3b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas_ta/__init__.py:7: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.\n", "  from pkg_resources import get_distribution, DistributionNotFound\n"]}, {"name": "stdout", "output_type": "stream", "text": ["********************************** \n", "Fetching data for 13 at interval daily\n", "*******************************************\n", "\n", "                  timestamp      open      high       low     close  \\\n", "0 2024-07-01 00:00:00+05:30  23992.95  24164.00  23992.70  24141.95   \n", "1 2024-07-02 00:00:00+05:30  24228.75  24236.35  24056.40  24123.85   \n", "2 2024-07-03 00:00:00+05:30  24291.75  24309.15  24207.10  24286.50   \n", "3 2024-07-04 00:00:00+05:30  24369.95  24401.00  24281.00  24302.15   \n", "4 2024-07-05 00:00:00+05:30  24213.35  24363.00  24168.85  24323.85   \n", "5 2024-07-08 00:00:00+05:30  24329.45  24344.60  24240.55  24320.55   \n", "6 2024-07-09 00:00:00+05:30  24351.00  24443.60  24331.90  24433.20   \n", "7 2024-07-10 00:00:00+05:30  24459.85  24461.05  24141.80  24324.45   \n", "8 2024-07-11 00:00:00+05:30  24396.55  24402.65  24193.75  24315.95   \n", "9 2024-07-12 00:00:00+05:30  24387.95  24592.20  24331.15  24502.15   \n", "\n", "        volume  \n", "0  242468081.0  \n", "1  309629240.0  \n", "2  289201551.0  \n", "3  251189844.0  \n", "4  298381204.0  \n", "5  266299131.0  \n", "6  250537091.0  \n", "7  292263786.0  \n", "8  306404194.0  \n", "9  325823474.0  \n", "                    timestamp      open      high       low     close  \\\n", "238 2025-06-16 00:00:00+05:30  24732.35  24967.10  24703.60  24946.50   \n", "239 2025-06-17 00:00:00+05:30  24977.85  24982.05  24813.70  24853.40   \n", "240 2025-06-18 00:00:00+05:30  24788.35  24947.55  24750.45  24812.05   \n", "241 2025-06-19 00:00:00+05:30  24803.25  24863.10  24733.40  24793.25   \n", "242 2025-06-20 00:00:00+05:30  24787.65  25136.20  24783.65  25112.40   \n", "243 2025-06-23 00:00:00+05:30  24939.75  25057.00  24824.85  24971.90   \n", "244 2025-06-24 00:00:00+05:30  25179.90  25317.70  24999.70  25044.35   \n", "245 2025-06-25 00:00:00+05:30  25150.35  25266.80  25125.05  25244.75   \n", "246 2025-06-26 00:00:00+05:30  25268.95  25565.30  25259.90  25549.00   \n", "247 2025-06-27 00:00:00+05:30  25576.65  25654.20  25523.55  25637.80   \n", "\n", "          volume  \n", "238  305813047.0  \n", "239  242415740.0  \n", "240  237599011.0  \n", "241  274619935.0  \n", "242  574654035.0  \n", "243  248534153.0  \n", "244  450185468.0  \n", "245  260582584.0  \n", "246  428891818.0  \n", "247  563957748.0  \n"]}, {"ename": "OSError", "evalue": "Cannot save file into a non-existent directory: 'test_multi_intru'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>\u001b[39m                                   <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 24\u001b[39m\n\u001b[32m     22\u001b[39m     \u001b[38;5;28mprint\u001b[39m(df.head(\u001b[32m10\u001b[39m))\n\u001b[32m     23\u001b[39m     \u001b[38;5;28mprint\u001b[39m(df.tail(\u001b[32m10\u001b[39m))\n\u001b[32m---> \u001b[39m\u001b[32m24\u001b[39m     \u001b[43mdf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtest_multi_intru/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43minstrument\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43msecurity_id\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m_\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43minterval\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m_data.csv\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     25\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     26\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mNo valid DataFrame returned for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minstrument[\u001b[33m'\u001b[39m\u001b[33msecurity_id\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m at interval \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minterval\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/util/_decorators.py:333\u001b[39m, in \u001b[36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    327\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) > num_allow_args:\n\u001b[32m    328\u001b[39m     warnings.warn(\n\u001b[32m    329\u001b[39m         msg.format(arguments=_format_argument_list(allow_args)),\n\u001b[32m    330\u001b[39m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[32m    331\u001b[39m         stacklevel=find_stack_level(),\n\u001b[32m    332\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m333\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/core/generic.py:3986\u001b[39m, in \u001b[36mNDFrame.to_csv\u001b[39m\u001b[34m(self, path_or_buf, sep, na_rep, float_format, columns, header, index, index_label, mode, encoding, compression, quoting, quotechar, lineterminator, chunksize, date_format, doublequote, escapechar, decimal, errors, storage_options)\u001b[39m\n\u001b[32m   3975\u001b[39m df = \u001b[38;5;28mself\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(\u001b[38;5;28mself\u001b[39m, ABCDataFrame) \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m.to_frame()\n\u001b[32m   3977\u001b[39m formatter = DataFrameFormatter(\n\u001b[32m   3978\u001b[39m     frame=df,\n\u001b[32m   3979\u001b[39m     header=header,\n\u001b[32m   (...)\u001b[39m\u001b[32m   3983\u001b[39m     decimal=decimal,\n\u001b[32m   3984\u001b[39m )\n\u001b[32m-> \u001b[39m\u001b[32m3986\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mDataFrameRenderer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mformatter\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_csv\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   3987\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpath_or_buf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3988\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlineterminator\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlineterminator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3989\u001b[39m \u001b[43m    \u001b[49m\u001b[43msep\u001b[49m\u001b[43m=\u001b[49m\u001b[43msep\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3990\u001b[39m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3991\u001b[39m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3992\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3993\u001b[39m \u001b[43m    \u001b[49m\u001b[43mquoting\u001b[49m\u001b[43m=\u001b[49m\u001b[43mquoting\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3994\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3995\u001b[39m \u001b[43m    \u001b[49m\u001b[43mindex_label\u001b[49m\u001b[43m=\u001b[49m\u001b[43mindex_label\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3996\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3997\u001b[39m \u001b[43m    \u001b[49m\u001b[43mchunksize\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunksize\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3998\u001b[39m \u001b[43m    \u001b[49m\u001b[43mquotechar\u001b[49m\u001b[43m=\u001b[49m\u001b[43mquotechar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3999\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdate_format\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdate_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4000\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdoublequote\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdoublequote\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4001\u001b[39m \u001b[43m    \u001b[49m\u001b[43mescapechar\u001b[49m\u001b[43m=\u001b[49m\u001b[43mescapechar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4002\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4003\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/io/formats/format.py:1014\u001b[39m, in \u001b[36mDataFrameRenderer.to_csv\u001b[39m\u001b[34m(self, path_or_buf, encoding, sep, columns, index_label, mode, compression, quoting, quotechar, lineterminator, chunksize, date_format, doublequote, escapechar, errors, storage_options)\u001b[39m\n\u001b[32m    993\u001b[39m     created_buffer = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m    995\u001b[39m csv_formatter = CSVFormatter(\n\u001b[32m    996\u001b[39m     path_or_buf=path_or_buf,\n\u001b[32m    997\u001b[39m     lineterminator=lineterminator,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1012\u001b[39m     formatter=\u001b[38;5;28mself\u001b[39m.fmt,\n\u001b[32m   1013\u001b[39m )\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m \u001b[43mcsv_formatter\u001b[49m\u001b[43m.\u001b[49m\u001b[43msave\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m created_buffer:\n\u001b[32m   1017\u001b[39m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(path_or_buf, StringIO)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/io/formats/csvs.py:251\u001b[39m, in \u001b[36mCSVFormatter.save\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    247\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    248\u001b[39m \u001b[33;03mCreate the writer & save.\u001b[39;00m\n\u001b[32m    249\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    250\u001b[39m \u001b[38;5;66;03m# apply compression and byte/text conversion\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m251\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    253\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    254\u001b[39m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    255\u001b[39m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    256\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    257\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    258\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m handles:\n\u001b[32m    259\u001b[39m     \u001b[38;5;66;03m# Note: self.encoding is irrelevant here\u001b[39;00m\n\u001b[32m    260\u001b[39m     \u001b[38;5;28mself\u001b[39m.writer = csvlib.writer(\n\u001b[32m    261\u001b[39m         handles.handle,\n\u001b[32m    262\u001b[39m         lineterminator=\u001b[38;5;28mself\u001b[39m.lineterminator,\n\u001b[32m   (...)\u001b[39m\u001b[32m    267\u001b[39m         quotechar=\u001b[38;5;28mself\u001b[39m.quotechar,\n\u001b[32m    268\u001b[39m     )\n\u001b[32m    270\u001b[39m     \u001b[38;5;28mself\u001b[39m._save()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/io/common.py:749\u001b[39m, in \u001b[36mget_handle\u001b[39m\u001b[34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[39m\n\u001b[32m    747\u001b[39m \u001b[38;5;66;03m# Only for write methods\u001b[39;00m\n\u001b[32m    748\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode \u001b[38;5;129;01mand\u001b[39;00m is_path:\n\u001b[32m--> \u001b[39m\u001b[32m749\u001b[39m     \u001b[43mcheck_parent_directory\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    751\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m compression:\n\u001b[32m    752\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m compression != \u001b[33m\"\u001b[39m\u001b[33mzstd\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m    753\u001b[39m         \u001b[38;5;66;03m# compression libraries do not like an explicit text-mode\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Algo-trade/Option_trade/.venv/lib/python3.11/site-packages/pandas/io/common.py:616\u001b[39m, in \u001b[36mcheck_parent_directory\u001b[39m\u001b[34m(path)\u001b[39m\n\u001b[32m    614\u001b[39m parent = Path(path).parent\n\u001b[32m    615\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m parent.is_dir():\n\u001b[32m--> \u001b[39m\u001b[32m616\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m(\u001b[33mrf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCannot save file into a non-existent directory: \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mparent\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mOSError\u001b[39m: Cannot save file into a non-existent directory: 'test_multi_intru'"]}], "source": ["\n", "import pandas as pd\n", "import datetime\n", "import utils.data_handler as data_handler\n", "\n", "\n", "instrument_list = [\n", "    {\"security_id\": \"13\", \"exchange_segment\": \"IDX_I\", \"instrument_type\": \"INDEX\"},    #NIFTY\n", "    {\"security_id\": \"25\", \"exchange_segment\": \"IDX_I\", \"instrument_type\": \"INDEX\"},    #BANKNIFTY\n", "    {\"security_id\": \"27\", \"exchange_segment\": \"IDX_I\", \"instrument_type\": \"INDEX\"}     #FINNIFTY\n", "    # {\"security_id\": \"INE467B01029\", \"exchange_segment\": \"NSE_EQ\"},\n", "    # Add more...\n", "]\n", "intervals_to_fetch = ['daily']\n", "for instrument in instrument_list:\n", "    for interval in intervals_to_fetch:\n", "    \n", "        print(\"********************************** \")\n", "        print(f\"Fetching data for {instrument['security_id']} at interval {interval}\")\n", "        print(\"*******************************************\\n\")\n", "        df, from_date, to_date = data_handler.fetch_historical_data(security_id=instrument[\"security_id\"], exchange_segment=instrument[\"exchange_segment\"], instrument_type=instrument[\"instrument_type\"], interval=interval)\n", "        if isinstance(df, pd.DataFrame) and not df.empty:\n", "            print(df.head(10))\n", "            print(df.tail(10))\n", "            df.to_csv(f\"test_multi_intru/{instrument['security_id']}_{interval}_data.csv\", index=False)\n", "        else:\n", "            print(f\"No valid DataFrame returned for {instrument['security_id']} at interval {interval}\")\n", "        \n", "    print(f\"Data fetched from {from_date} to {to_date} for {instrument['security_id']} on {instrument['exchange_segment']}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Option_trade", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}