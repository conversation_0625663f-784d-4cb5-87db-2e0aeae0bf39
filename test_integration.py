#!/usr/bin/env python3
"""
Test script for the integration of calculate_wma9_color_code with process_dataframe.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the utils directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from processing_functions import process_dataframe

def create_sample_price_data():
    """Create sample price data for testing."""
    
    # Create realistic price data
    np.random.seed(42)  # For reproducible results
    
    n_periods = 100
    base_price = 100
    
    # Generate price data with some trend and volatility
    price_changes = np.random.normal(0, 0.5, n_periods)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] + change
        prices.append(max(new_price, 50))  # Ensure price doesn't go too low
    
    # Create OHLC data
    data = {
        'timestamp': pd.date_range('2025-01-01 09:15:00', periods=n_periods, freq='1min'),
        'open': prices,
        'high': [p + abs(np.random.normal(0, 0.2)) for p in prices],
        'low': [p - abs(np.random.normal(0, 0.2)) for p in prices],
        'close': prices,
        'volume': [np.random.randint(1000, 10000) for _ in range(n_periods)]
    }
    
    df = pd.DataFrame(data)
    
    # Ensure high >= close >= low
    df['high'] = df[['high', 'close']].max(axis=1)
    df['low'] = df[['low', 'close']].min(axis=1)
    
    return df

def test_integration():
    """Test the integration of WMA9 color code with the main processing function."""
    
    print("Testing integration of calculate_wma9_color_code with process_dataframe...")
    
    # Create sample data
    df_sample = create_sample_price_data()
    
    print(f"\nInput DataFrame shape: {df_sample.shape}")
    print("Input columns:", df_sample.columns.tolist())
    
    # Process the DataFrame
    try:
        result_df, zones_affected = process_dataframe(df_sample)
        
        print(f"\nProcessed DataFrame shape: {result_df.shape}")
        print("Output columns:", result_df.columns.tolist())
        
        # Check if WMA9_color_code column exists
        if 'WMA9_color_code' in result_df.columns:
            print("\n✓ WMA9_color_code column successfully added!")
            
            # Show color distribution
            color_counts = result_df['WMA9_color_code'].value_counts()
            print("\nColor distribution:")
            for color, count in color_counts.items():
                if color != '':
                    print(f"  {color}: {count} occurrences")
            
            # Show first few rows with colors
            colored_rows = result_df[result_df['WMA9_color_code'] != '']
            if not colored_rows.empty:
                print(f"\nFirst 10 rows with colors:")
                display_cols = ['timestamp', 'close', 'crossover_signal_5_9', 'WMA9_color_code']
                available_cols = [col for col in display_cols if col in result_df.columns]
                print(colored_rows[available_cols].head(10).to_string(index=False))
            
            # Check for color transitions
            print(f"\nColor transitions:")
            prev_color = ''
            transition_count = 0
            for i, row in result_df.iterrows():
                current_color = row['WMA9_color_code']
                if current_color != '' and current_color != prev_color and prev_color != '':
                    transition_count += 1
                    print(f"  {prev_color} → {current_color} at {row['timestamp']}")
                if current_color != '':
                    prev_color = current_color
            
            print(f"\nTotal color transitions: {transition_count}")
            
        else:
            print("\n✗ WMA9_color_code column not found!")
            
        # Check crossover signal columns
        crossover_cols = [col for col in result_df.columns if 'crossover_signal' in col]
        print(f"\nCrossover signal columns found: {len(crossover_cols)}")
        for col in crossover_cols:
            non_empty = (result_df[col] != ' ').sum()
            print(f"  {col}: {non_empty} signals")
            
    except Exception as e:
        print(f"\n✗ Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n=== Integration test completed successfully! ===")
    return True

if __name__ == "__main__":
    test_integration()
